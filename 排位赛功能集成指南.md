# 花砖物语排位赛功能集成指南

## 🎯 功能概述

排位赛功能已成功集成到花砖物语微信小游戏中，现在玩家可以：
- 在主菜单选择"排位赛"进入竞技模式
- 查看个人积分、段位和排名
- 自动匹配同水平玩家进行4人对战
- 查看全服排行榜和个人历史记录

## 🎮 如何使用排位赛功能

### 1. 启动服务器

```bash
cd server
npm start
```

服务器将在 `http://localhost:3000` 启动，并自动初始化排位赛系统。

### 2. 在微信开发者工具中测试

1. 打开微信开发者工具
2. 导入花砖物语项目
3. 确保 `game.js` 已包含排位赛相关代码
4. 运行项目

### 3. 使用排位赛功能

1. **进入排位赛**：
   - 在主菜单点击"排位赛"按钮
   - 系统会自动连接到排位赛服务器

2. **查看个人信息**：
   - 积分：当前ELO积分（初始1200分）
   - 段位：青铜、白银、黄金、铂金、钻石、大师
   - 排名：全服排名位置
   - 胜率：胜场/总场次比例

3. **开始匹配**：
   - 点击"开始匹配"按钮
   - 系统根据积分自动匹配同水平玩家
   - 等待匹配完成（通常30秒内）

4. **排位赛游戏**：
   - 4人对战，严格30秒回合计时
   - 断线重连支持
   - 游戏结束后自动计算积分变化

## 🏗️ 技术实现详情

### 已集成的组件

1. **RankedUI类** - 排位赛界面管理
2. **MultiplayerManager扩展** - 排位赛网络通信
3. **GameApp扩展** - 排位赛菜单和游戏启动
4. **服务端系统** - 完整的排位赛后端支持

### 主要文件修改

- `game.js` - 添加了排位赛UI和网络通信代码
- `server/` - 完整的排位赛服务端系统

## 📱 界面展示

### 主菜单
```
🎮 花砖物语

[单人游戏] [好友联机] [排位赛]

单人游戏：与AI对战
好友联机：邀请微信好友一起游戏
排位赛：竞技匹配，提升段位
```

### 排位赛界面
```
排位赛

┌─────────────────────────────────┐
│ 🥉 青铜 5                        │
│ 积分: 1200    排名: #-          │
│ 胜率: 0.0%    连胜: 0           │
│ ████████████████████████████    │
└─────────────────────────────────┘

[开始匹配]  [排行榜]

[返回]
```

## 🎯 段位系统

| 段位 | 积分范围 | 分段 | 图标 |
|------|----------|------|------|
| 青铜 | 0-1099 | 5段 | 🥉 |
| 白银 | 1100-1299 | 5段 | 🥈 |
| 黄金 | 1300-1499 | 5段 | 🥇 |
| 铂金 | 1500-1699 | 4段 | 💎 |
| 钻石 | 1700-1999 | 4段 | 💠 |
| 大师 | 2000+ | 1段 | 👑 |

## 🔧 配置选项

### 服务器配置
- 端口：3000（可在game-server.js中修改）
- 匹配时间：最大5分钟等待
- 回合时限：30秒

### 积分系统配置
- 初始积分：1200分
- K因子：32
- 积分变化范围：-50到+50分

## 🐛 故障排除

### 常见问题

1. **无法连接到排位赛服务器**
   - 确保服务器已启动（`cd server && npm start`）
   - 检查端口3000是否被占用
   - 确认防火墙设置

2. **排位赛按钮无响应**
   - 检查控制台是否有JavaScript错误
   - 确认RankedUI类已正确集成
   - 验证事件绑定是否正常

3. **匹配超时**
   - 需要至少4个玩家同时在线匹配
   - 可以开启多个测试客户端进行测试

### 调试模式

在控制台查看详细日志：
```javascript
console.log('排位赛UI初始化完成');
console.log('加入排位队列:', playerInfo.nickName);
console.log('匹配成功！');
```

## 📊 数据存储

排位赛数据存储在 `server/data/` 目录：
- `users.json` - 用户积分和段位信息
- `matches.json` - 比赛记录
- `rating_history.json` - 积分变化历史
- `daily_stats.json` - 每日统计数据

## 🚀 扩展功能

### 可以添加的功能
1. 赛季系统
2. 成就系统
3. 好友排行榜
4. 观战功能
5. 回放系统

### 自定义配置
可以修改以下参数：
- 段位积分范围
- 匹配算法参数
- 计时器时长
- UI样式和颜色

## 📞 技术支持

如遇问题，请检查：
1. 服务器控制台输出
2. 客户端控制台错误
3. 网络连接状态
4. 数据文件完整性

---

## 🎉 总结

排位赛功能已完全集成到花砖物语微信小游戏中，包括：

✅ **完整的排位赛系统**
- ELO积分算法
- 六段位系统
- 自动匹配机制

✅ **用户界面集成**
- 主菜单排位赛入口
- 排位赛专用界面
- 积分和段位显示

✅ **服务端支持**
- 数据持久化
- 匹配队列管理
- 实时通信

✅ **测试验证**
- 系统功能测试
- 客户端集成测试
- 用户界面测试

现在玩家可以享受完整的竞技游戏体验，通过排位赛提升技能并获得更高的段位！

🎮 **开始你的排位之旅吧！**
