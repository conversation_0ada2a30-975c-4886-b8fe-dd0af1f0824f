# 花砖物语排位赛端口配置说明

## 🔧 当前端口配置

现在使用端口 **3000**

### 已修改的文件

1. **server/game-server.js**
   ```javascript
   const PORT = process.env.PORT || 3000;
   ```

2. **game.js** (排位赛连接)
   ```javascript
   const initPromise = this.multiplayerManager.init({
     serverUrl: 'ws://localhost:3000' // 排位赛服务器端口
   });
   ```

3. **test-ranked-client.html**
   ```javascript
   socket = new WebSocket('ws://localhost:3000', ['game-protocol']);
   ```

## 🚀 启动服务器

```bash
cd server
node game-server.js
```

服务器将在以下地址启动：
- HTTP服务器: http://localhost:3000
- WebSocket服务器: ws://localhost:3000

## ✅ 验证服务器状态

服务器启动成功后会显示：
```
🎮 花砖物语游戏服务器启动成功！
HTTP服务器: http://localhost:3000
WebSocket服务器: ws://localhost:3000
✅ 排位赛系统初始化完成
```

## 🔍 故障排除

### 如果端口3000被占用

1. **检查端口占用**：
   ```bash
   netstat -ano | findstr :3000
   ```

2. **使用其他端口**：
   修改 `server/game-server.js` 中的端口号，例如：
   ```javascript
   const PORT = process.env.PORT || 3001;
   ```

3. **同时修改客户端连接**：
   修改 `game.js` 中的连接地址：
   ```javascript
   serverUrl: 'ws://localhost:3001'
   ```

### 连接问题排查

1. **确认服务器已启动**
2. **检查防火墙设置**
3. **确认端口号一致**
4. **查看控制台错误信息**

## 📝 注意事项

- 好友联机功能仍然使用原来的端口配置
- 排位赛功能使用端口3000
- 如需修改端口，请同时修改服务端和客户端配置

## 🎮 测试排位赛功能

1. 启动服务器（端口3000）
2. 在微信开发者工具中运行游戏
3. 点击主菜单的"排位赛"按钮
4. 查看是否能正常连接和显示排位信息

如果连接成功，你应该能看到排位赛界面显示用户的积分、段位等信息。
