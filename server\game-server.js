// 花砖物语游戏服务器 - Node.js + WebSocket
const WebSocket = require('ws');
const http = require('http');
const express = require('express');
const cors = require('cors');

// 导入游戏逻辑模块
const AzulGameEngine = require('./azulGameEngine');
const GameValidator = require('./gameValidator');
const ScoringSystem = require('./scoringSystem');

// 导入排位赛系统模块
const DatabaseManager = require('./database');
const RankingSystem = require('./rankingSystem');
const MatchmakingSystem = require('./matchmakingSystem');
const RankedGameEngine = require('./rankedGameEngine');

// 创建Express应用
const app = express();
app.use(cors());
app.use(express.json());

// 创建HTTP服务器
const server = http.createServer(app);

// 创建WebSocket服务器
const wss = new WebSocket.Server({ 
  server,
  protocols: ['game-protocol']
});

// 全局连接管理（与房间无关，持久化连接）
const globalConnections = {
  players: new Map(),    // playerId -> { ws, connectTime, info }
};

// 游戏数据存储（生产环境建议使用Redis或数据库）
const gameData = {
  rooms: new Map(),      // 房间数据
  roomPlayers: new Map(), // 房间-玩家映射
  roomCleanupTimers: new Map(), // 房间清理计时器
  requestLimits: new Map() // 请求频率限制
};

// 初始化游戏系统
const gameEngine = new AzulGameEngine();
const gameValidator = new GameValidator();
const scoringSystem = new ScoringSystem();

// 初始化排位赛系统
let databaseManager = null;
let rankingSystem = null;
let matchmakingSystem = null;
let rankedGameEngine = null;

// 异步初始化排位赛系统
async function initRankedSystems() {
  try {
    databaseManager = new DatabaseManager();
    await databaseManager.init();

    rankingSystem = new RankingSystem();
    rankedGameEngine = new RankedGameEngine();
    // 创建一个临时的gameServer对象，稍后会被完整的服务器实例替换
    const gameServerRef = {
      createRankedRoom: (matchId, matchData) => {
        console.log(`创建排位赛房间: ${matchId}`);

        // 清除可能存在的旧清理定时器
        clearRoomCleanupTimer(matchId);

        // 将排位赛房间添加到房间管理系统
        const room = {
          id: matchId,
          type: 'ranked',
          players: matchData.players.map(p => ({
            id: p.id,
            nickName: p.nickName,
            ws: p.ws
          })),
          gameState: matchData.gameState,
          maxPlayers: 4,
          currentPlayers: matchData.players.length,
          isRanked: true,
          matchData: matchData,
          status: 'playing', // 排位赛房间状态为游戏中
          config: {
            playerCount: 4,
            aiCount: 0,
            isRanked: true
          }
        };

        gameData.rooms.set(matchId, room);

        // 清理玩家的旧房间映射
        const playerIds = new Set(matchData.players.map(p => p.id));

        // 从所有旧房间中移除这些玩家
        for (const [oldRoomId, oldPlayerIds] of gameData.roomPlayers) {
          if (oldRoomId !== matchId) {
            let hasChanges = false;
            for (const playerId of playerIds) {
              if (oldPlayerIds.has(playerId)) {
                oldPlayerIds.delete(playerId);
                hasChanges = true;
                console.log(`从旧房间 ${oldRoomId} 中移除玩家 ${playerId}`);
              }
            }

            // 如果房间没有玩家了，删除房间映射
            if (oldPlayerIds.size === 0) {
              gameData.roomPlayers.delete(oldRoomId);
              console.log(`删除空房间映射: ${oldRoomId}`);
            }
          }
        }

        // 将玩家添加到新房间映射
        gameData.roomPlayers.set(matchId, playerIds);
        console.log(`创建新房间映射: ${matchId}, 玩家: ${Array.from(playerIds).join(', ')}`);

        // 更新玩家连接状态，确保WebSocket连接是最新的
        console.log(`开始同步玩家连接，matchData.players数量: ${matchData.players.length}`);
        console.log(`同步前全局连接管理器玩家数: ${globalConnections.players.size}`);

        matchData.players.forEach((player, index) => {
          console.log(`同步玩家${index + 1}: ${player.id} (${player.nickName}), ws状态: ${player.ws ? player.ws.readyState : 'null'}`);

          const existingPlayer = globalConnections.players.get(player.id);
          if (existingPlayer) {
            // 更新现有玩家的连接状态
            existingPlayer.ws = player.ws;
            console.log(`✅ 更新玩家 ${player.id} 的连接状态`);
          } else {
            // 添加新的玩家连接
            globalConnections.players.set(player.id, {
              ws: player.ws,
              info: { nickName: player.nickName },
              connectTime: Date.now()
            });
            console.log(`✅ 添加玩家 ${player.id} 的连接状态`);
          }
        });

        console.log(`同步后全局连接管理器玩家数: ${globalConnections.players.size}`);

        // 启动游戏计时器
        if (gameEngine && matchData.gameState) {
          console.log(`启动排位赛计时器，房间: ${matchId}`);
          gameEngine.startTurnTimer(matchId, matchData.gameState, (newGameState, aiMove) => {
            // 排位赛超时处理回调
            console.log(`排位赛回合超时，房间: ${matchId}`);
            console.log('AI代替超时玩家执行动作:', aiMove);

            // 获取超时玩家信息（在更新游戏状态之前获取）
            const room = gameData.rooms.get(matchId);
            if (!room) {
              console.error(`排位赛房间 ${matchId} 不存在`);
              return;
            }

            const timeoutPlayerId = room.gameState.players[room.gameState.currentPlayer].id;

            // 更新房间状态
            room.gameState = newGameState;
            room.lastUpdate = Date.now();

            // 广播超时和AI动作给排位赛房间的所有玩家
            broadcastToRoom(matchId, 'playerTimeout', {
              timeoutPlayerId: timeoutPlayerId,
              aiMove: aiMove,
              gameState: gameEngine.getGameStateSummary(newGameState),
              timestamp: Date.now()
            });

            // 根据游戏阶段继续处理
            if (newGameState.phase === 'scoring') {
              handleScoringPhase(matchId, room);
            } else if (newGameState.phase === 'end') {
              handleGameEnd(matchId, room);
            } else {
              // 正常游戏继续，广播状态更新并启动下一个玩家的计时器
              broadcastToRoom(matchId, 'gameStateUpdate', {
                gameState: gameEngine.getGameStateSummary(newGameState),
                lastAction: {
                  playerId: timeoutPlayerId,
                  action: aiMove,
                  timestamp: Date.now(),
                  isAI: true
                },
                turnTimeLimit: gameEngine.turnTimeLimit,
                remainingTime: gameEngine.turnTimeLimit // 新回合开始，满时间
              });

              // 启动下一个玩家的计时器
              startTurnTimer(matchId, room);
            }
          });
        }

        console.log(`✅ 排位赛房间 ${matchId} 创建成功，玩家数: ${matchData.players.length}`);
      }
    };

    matchmakingSystem = new MatchmakingSystem(rankingSystem, databaseManager, gameServerRef);

    console.log('✅ 排位赛系统初始化完成');
  } catch (error) {
    console.error('❌ 排位赛系统初始化失败:', error);
  }
}

// WebSocket连接处理
wss.on('connection', (ws, request) => {
  console.log('新的WebSocket连接');
  
  let playerId = null;
  let currentRoomId = null;

  // 消息处理
  ws.on('message', (data) => {
    try {
      const message = JSON.parse(data);
      console.log('收到消息:', message);
      
      handleMessage(ws, message);
    } catch (error) {
      console.error('解析消息失败:', error);
      sendError(ws, '消息格式错误');
    }
  });

  // 连接关闭处理
  ws.on('close', () => {
    console.log('WebSocket连接关闭:', playerId);
    handlePlayerDisconnect(playerId, currentRoomId);
  });

  // 错误处理
  ws.on('error', (error) => {
    console.error('WebSocket错误:', error);
  });

  // 设置连接状态跟踪
  ws.playerId = null;
  ws.roomId = null;

  // 请求频率限制检查
  function checkRateLimit(playerId, messageType, limitMs = 100) {
    const key = `${playerId}_${messageType}`;
    const now = Date.now();
    const lastRequest = gameData.requestLimits.get(key) || 0;

    if (now - lastRequest < limitMs) {
      return false; // 请求过于频繁
    }

    gameData.requestLimits.set(key, now);
    return true; // 允许请求
  }

  // 消息处理函数
  function handleMessage(ws, message) {
    const { type, data } = message;

    // 对于需要频率限制的消息类型进行检查
    if (['gameAction', 'createRoom', 'startGame'].includes(type)) {
      if (playerId && !checkRateLimit(playerId, type)) {
        console.log(`请求过于频繁，忽略: ${playerId} - ${type}`);
        return;
      }
    }

    switch (type) {
      case 'auth':
        handleAuth(ws, data);
        break;
      case 'createRoom':
        handleCreateRoom(ws, data);
        break;
      case 'joinRoom':
        handleJoinRoom(ws, data);
        break;
      case 'leaveRoom':
        handleLeaveRoom(ws, data);
        break;
      case 'startGame':
        handleStartGame(ws, data);
        break;
      case 'gameAction':
        handleGameAction(ws, data);
        break;
      case 'syncGameState':
        handleSyncGameState(ws, data);
        break;
      case 'getRemainingTime':
        handleGetRemainingTime(ws, data);
        break;
      case 'ping':
        handlePing(ws, data);
        break;
      case 'chatMessage':
        handleChatMessage(ws, data);
        break;
      case 'getRoomList':
        handleGetRoomList(ws);
        break;
      case 'joinRankedQueue':
        handleJoinRankedQueue(ws, data);
        break;
      case 'leaveRankedQueue':
        handleLeaveRankedQueue(ws, data);
        break;
      case 'getUserRanking':
        handleGetUserRanking(ws, data);
        break;
      case 'getLeaderboard':
        handleGetLeaderboard(ws, data);
        break;
      case 'getUserHistory':
        handleGetUserHistory(ws, data);
        break;

      default:
        sendError(ws, '未知的消息类型: ' + type);
    }
  }

  // 认证处理
  function handleAuth(ws, data) {
    const newPlayerId = data.playerId;
    const playerInfo = data.playerInfo;

    console.log(`处理玩家认证: ${newPlayerId}, 当前连接的playerId: ${playerId}`);

    // 防止重复认证
    if (playerId && playerId === newPlayerId) {
      console.log('玩家重复认证，忽略:', playerId);
      return;
    }

    // 检查是否有其他连接使用相同的playerId
    const existingPlayer = globalConnections.players.get(newPlayerId);
    if (existingPlayer && existingPlayer.ws !== ws) {
      console.log('检测到重复连接，关闭旧连接:', newPlayerId);
      // 关闭旧连接
      if (existingPlayer.ws && existingPlayer.ws.readyState === WebSocket.OPEN) {
        existingPlayer.ws.close(1000, '新连接已建立');
      }
    }

    playerId = newPlayerId;

    // 更新WebSocket连接状态
    ws.playerId = playerId;

    // 存储玩家连接到全局连接管理器
    globalConnections.players.set(playerId, {
      ws: ws,
      info: playerInfo,
      connectTime: Date.now()
    });

    console.log(`玩家认证成功: ${playerId}, 当前在线玩家数: ${globalConnections.players.size}`);
    sendMessage(ws, 'authSuccess', { playerId });

    // 检查玩家是否之前在某个房间中（冷启动重连）
    console.log(`检查玩家 ${playerId} 是否在房间中...`);
    console.log(`当前房间数量: ${gameData.rooms.size}`);

    const roomId = findPlayerRoom(playerId);
    if (roomId) {
      console.log(`玩家 ${playerId} 重新连接到房间 ${roomId}`);

      const room = gameData.rooms.get(roomId);
      if (room) {
        // 检查房间状态，如果游戏已结束，不发送重连信息
        if (room.status === 'finished') {
          console.log(`房间 ${roomId} 游戏已结束，不发送冷启动重连信息`);

          // 可以选择立即清理房间，或者让定时器处理
          // cleanupRoom(roomId, '玩家尝试重连到已结束的游戏');

          return;
        }

        // 取消清理计时器
        clearRoomCleanupTimer(roomId);

        // 发送重连信息给客户端
        const reconnectData = {
          type: 'coldReconnect',
          roomId: roomId,
          players: room.players,
          config: room.config,
          roomStatus: room.status
        };

        // 如果游戏已经开始，包含游戏状态
        if (room.status === 'playing' && room.gameState) {
          reconnectData.gameState = gameEngine.getGameStateSummary(room.gameState);
          reconnectData.isGameStarted = true;

          // 如果有剩余时间，也发送
          const remainingTime = getRemainingTime(roomId);
          if (remainingTime > 0) {
            reconnectData.remainingTime = remainingTime;
          }
        }

        sendMessage(ws, 'coldReconnect', reconnectData);

        // 通知其他玩家该玩家重新连接
        broadcastToRoom(roomId, 'playerReconnected', {
          playerId: playerId,
          timestamp: Date.now()
        }, playerId);
      }
    } else {
      console.log(`玩家 ${playerId} 没有在任何房间中，无需重连`);
    }
  }

  // 创建房间
  function handleCreateRoom(ws, data) {
    const { roomId, hostId, hostInfo, config } = data;

    console.log(`处理创建房间请求: roomId=${roomId}, hostId=${hostId}, playerId=${playerId}`);

    // 验证玩家是否已认证
    if (!playerId || playerId !== hostId) {
      console.log(`房间创建失败: 玩家未认证或认证信息不匹配, playerId=${playerId}, hostId=${hostId}`);
      sendError(ws, '玩家未认证或认证信息不匹配');
      return;
    }

    // 检查玩家是否已在其他房间中
    const existingRoomId = findPlayerRoom(hostId);
    if (existingRoomId) {
      const existingRoom = gameData.rooms.get(existingRoomId);
      console.log(`玩家 ${hostId} 已在房间 ${existingRoomId} 中 (状态: ${existingRoom ? existingRoom.status : 'unknown'})，无法创建新房间`);

      // 如果是已结束的房间，允许创建新房间
      if (existingRoom && existingRoom.status === 'finished') {
        console.log(`房间 ${existingRoomId} 已结束，允许玩家创建新房间`);
        // 从旧房间中移除玩家
        handlePlayerLeaveRoom(existingRoomId, hostId);
      } else {
        sendError(ws, '您已在其他房间中，请先离开当前房间');
        return;
      }
    }

    if (gameData.rooms.has(roomId)) {
      console.log(`房间创建失败: 房间ID ${roomId} 已存在`);
      sendError(ws, '房间ID已存在');
      return;
    }

    const room = {
      id: roomId,
      hostId: hostId,
      hostInfo: hostInfo,
      config: config,
      players: [hostInfo],
      gameState: null,
      status: 'waiting', // waiting, playing, finished
      createTime: Date.now(),
      lastUpdate: Date.now()
    };

    gameData.rooms.set(roomId, room);
    gameData.roomPlayers.set(roomId, new Set([hostId]));
    currentRoomId = roomId;

    console.log('房间创建成功:', roomId);
    sendMessage(ws, 'roomCreated', {
      roomId: roomId,
      players: room.players,
      config: config
    });
  }

  // 加入房间
  function handleJoinRoom(ws, data) {
    const { roomId, playerId, playerInfo } = data;

    const room = gameData.rooms.get(roomId);
    if (!room) {
      sendError(ws, '房间不存在');
      return;
    }

    // 验证玩家是否已认证
    if (!playerId) {
      sendError(ws, '玩家未认证');
      return;
    }

    // 检查是否已在房间中（重连情况）
    const existingPlayer = room.players.find(p => p.id === playerId);
    const isReconnecting = !!existingPlayer;

    if (!isReconnecting) {
      // 新玩家加入的检查
      if (room.status !== 'waiting') {
        sendError(ws, '房间已开始游戏，无法加入新玩家');
        return;
      }

      if (room.players.length >= room.config.playerCount) {
        sendError(ws, '房间已满');
        return;
      }

      // 添加新玩家
      room.players.push(playerInfo);
      gameData.roomPlayers.get(roomId).add(playerId);
      console.log('新玩家加入房间:', playerId, roomId);
    } else {
      // 重连情况
      console.log('玩家重新连接到房间:', playerId, roomId);



      // 清除房间清理计时器（如果有的话）
      clearRoomCleanupTimer(roomId);
    }

    room.lastUpdate = Date.now();
    currentRoomId = roomId;

    if (isReconnecting) {
      // 重连情况：发送当前游戏状态
      const responseData = {
        roomId: roomId,
        players: room.players,
        config: room.config
      };

      // 如果游戏已经开始，包含游戏状态
      if (room.status === 'playing' && room.gameState) {
        responseData.gameState = gameEngine.getGameStateSummary(room.gameState);
        responseData.isGameStarted = true;

        // 如果有剩余时间，也发送
        const remainingTime = getRemainingTime(roomId);
        if (remainingTime > 0) {
          responseData.remainingTime = remainingTime;
        }
      }

      sendMessage(ws, 'roomJoined', responseData);

      // 通知房间内其他玩家有人重连
      broadcastToRoom(roomId, 'playerReconnected', {
        player: existingPlayer,
        players: room.players
      }, playerId);
    } else {
      // 新加入情况
      sendMessage(ws, 'roomJoined', {
        roomId: roomId,
        players: room.players,
        config: room.config
      });

      // 通知房间内其他玩家
      broadcastToRoom(roomId, 'playerJoined', {
        player: playerInfo,
        players: room.players
      }, playerId);
    }
  }

  // 离开房间
  function handleLeaveRoom(ws, data) {
    const { roomId, playerId } = data;

    console.log(`玩家 ${playerId} 请求离开房间: ${roomId}`);

    // 检查是否为排位赛房间
    if (roomId && roomId.startsWith('ranked_')) {
      console.log('处理排位赛房间离开请求');

      // 对于排位赛房间，从全局连接管理器中移除玩家
      globalConnections.players.delete(playerId);
      console.log(`移除排位赛玩家 ${playerId} 的连接`);

      // 发送确认消息
      if (ws && ws.readyState === 1) {
        ws.send(JSON.stringify({
          type: 'roomLeft',
          data: { success: true, roomId: roomId }
        }));
      }

      // 检查是否所有玩家都已离线，如果是则立即清理房间
      const room = gameData.rooms.get(roomId);
      if (room && room.status === 'finished') {
        const roomPlayerIds = gameData.roomPlayers.get(roomId);
        if (roomPlayerIds) {
          let allOffline = true;
          for (const pid of roomPlayerIds) {
            const playerConnection = globalConnections.players.get(pid);
            if (playerConnection && playerConnection.ws.readyState === WebSocket.OPEN) {
              allOffline = false;
              break;
            }
          }

          if (allOffline) {
            console.log(`排位赛房间 ${roomId} 所有玩家已离线，立即清理`);
            // 清除自动清理定时器
            clearRoomCleanupTimer(roomId);
            // 立即清理房间
            cleanupRoom(roomId, '所有玩家离线后立即清理');
          }
        }
      }
    } else {
      // 普通房间的离开处理
      handlePlayerLeaveRoom(roomId, playerId);
    }
  }

  // 开始游戏
  function handleStartGame(ws, data) {
    const { roomId } = data;
    const room = gameData.rooms.get(roomId);

    if (!room) {
      sendError(ws, '房间不存在');
      return;
    }

    if (room.hostId !== playerId) {
      sendError(ws, '只有房主可以开始游戏');
      return;
    }

    // 检查游戏是否已经开始
    if (room.status === 'playing' && room.gameState) {
      console.log('游戏已经开始，忽略重复的开始请求');
      // 发送当前游戏状态给请求者
      ws.send(JSON.stringify({
        type: 'gameStarted',
        data: {
          gameState: gameEngine.getGameStateSummary(room.gameState),
          turnTimeLimit: gameEngine.turnTimeLimit,
          remainingTime: gameEngine.turnTimeLimit
        }
      }));
      return;
    }

    try {
      // 使用游戏引擎创建游戏状态
      const serverGameState = gameEngine.createGame(room.players);

      // 验证游戏状态
      const validation = gameValidator.validateGameState(serverGameState);
      if (!validation.valid) {
        console.error('游戏状态验证失败:', validation.errors);
        sendError(ws, '游戏初始化失败');
        return;
      }

      room.status = 'playing';
      room.gameState = serverGameState;
      room.startTime = Date.now();
      room.lastUpdate = Date.now();

      console.log('游戏开始:', roomId, '玩家数量:', room.players.length);
      console.log('工厂数量:', serverGameState.factories.length);

      // 通知房间内所有玩家
      broadcastToRoom(roomId, 'gameStarted', {
        gameState: gameEngine.getGameStateSummary(serverGameState),
        turnTimeLimit: gameEngine.turnTimeLimit,
        remainingTime: gameEngine.turnTimeLimit // 游戏开始，满时间
      });

      // 启动第一个玩家的回合计时器
      startTurnTimer(roomId, room);

    } catch (error) {
      console.error('开始游戏失败:', error);
      sendError(ws, '游戏初始化失败: ' + error.message);
    }
  }



  // 游戏动作处理
  function handleGameAction(ws, data) {
    const { roomId, playerId, action } = data;
    const room = gameData.rooms.get(roomId);

    if (!room) {
      sendError(ws, '房间不存在');
      return;
    }

    if (room.status !== 'playing') {
      sendError(ws, '游戏未开始');
      return;
    }

    // 验证玩家身份
    if (!playerId || playerId !== ws.playerId) {
      sendError(ws, '玩家身份验证失败');
      return;
    }

    // 验证是否轮到该玩家
    if (room.gameState.currentPlayer !== room.gameState.players.findIndex(p => p.id === playerId)) {
      sendError(ws, '不是您的回合');
      return;
    }

    // 防止重复处理相同的动作（简单的时间戳检查）
    const now = Date.now();
    const lastActionKey = `${roomId}_${playerId}_lastAction`;
    const lastActionTime = gameData[lastActionKey] || 0;

    if (now - lastActionTime < 100) { // 100ms内的重复动作将被忽略
      console.log('忽略重复的游戏动作:', playerId, action);
      return;
    }
    gameData[lastActionKey] = now;

    console.log('收到游戏动作:', playerId, action);

    try {
      // 验证动作的完整性
      const validation = gameValidator.validateMoveIntegrity(room.gameState, playerId, action);
      if (!validation.valid) {
        console.error('动作验证失败:', validation.errors);
        sendError(ws, '无效动作: ' + validation.errors.join(', '));
        return;
      }

      // 执行动作
      const result = gameEngine.executeMove(room.gameState, playerId, action);
      if (!result.success) {
        console.error('动作执行失败:', result.error);
        sendError(ws, '动作执行失败: ' + result.error);
        return;
      }

      // 更新房间状态
      room.gameState = result.gameState;
      room.lastUpdate = Date.now();

      console.log('动作执行成功，当前阶段:', room.gameState.phase);

      // 根据游戏阶段发送不同的响应
      if (room.gameState.phase === 'scoring') {
        // 计分阶段
        handleScoringPhase(roomId, room);
      } else if (room.gameState.phase === 'end') {
        // 游戏结束
        handleGameEnd(roomId, room);
      } else {
        // 正常游戏继续，启动下一个玩家的计时器
        broadcastToRoom(roomId, 'gameStateUpdate', {
          gameState: gameEngine.getGameStateSummary(room.gameState),
          lastAction: {
            playerId: playerId,
            action: action,
            timestamp: Date.now()
          },
          turnTimeLimit: gameEngine.turnTimeLimit,
          remainingTime: gameEngine.turnTimeLimit // 新回合开始，满时间
        });

        // 启动新的回合计时器
        startTurnTimer(roomId, room);
      }

    } catch (error) {
      console.error('处理游戏动作时发生错误:', error);
      sendError(ws, '服务器错误: ' + error.message);
    }
  }

  // 处理计分阶段（移动到全局作用域）
  // 此函数已移动到全局作用域

  // 处理游戏结束（移动到全局作用域）
  // 此函数已移动到全局作用域

  // 启动回合计时器（移动到全局作用域）
  // 此函数已移动到全局作用域

  // 停止回合计时器（移动到全局作用域）
  // 此函数已移动到全局作用域

  // 获取剩余时间（移动到全局作用域）
  // 此函数已移动到全局作用域

  // 检查房间是否所有玩家都掉线（移动到全局作用域）
  // 此函数已移动到全局作用域

  // 启动房间清理计时器（移动到全局作用域）
  // 此函数已移动到全局作用域

  // 清除房间清理计时器（移动到全局作用域）
  // 此函数已移动到全局作用域

  // 清理房间（移动到全局作用域）
  // 此函数已移动到全局作用域

  // 获取剩余时间（移动到全局作用域）
  // 此函数已移动到全局作用域



  // 同步游戏状态（保留用于兼容性，但不再推荐使用）
  function handleSyncGameState(ws, data) {
    console.warn('收到同步游戏状态请求，但现在游戏逻辑由服务端控制');
    sendError(ws, '游戏状态由服务端控制，无法手动同步');
  }

  // 聊天消息处理
  function handleChatMessage(ws, data) {
    const { roomId, playerId, playerInfo, text } = data;

    console.log('聊天消息:', playerId, text);

    // 广播给房间内所有玩家
    broadcastToRoom(roomId, 'chatMessage', {
      playerId: playerId,
      playerInfo: playerInfo,
      text: text,
      timestamp: Date.now()
    });
  }

  // 获取房间列表
  function handleGetRoomList(ws) {
    const roomList = [];
    
    for (const [roomId, room] of gameData.rooms) {
      if (room.status === 'waiting') {
        roomList.push({
          roomId: roomId,
          hostName: room.hostInfo.nickName,
          playerCount: room.players.length,
          maxPlayers: room.config.playerCount,
          createTime: room.createTime
        });
      }
    }

    sendMessage(ws, 'roomList', { rooms: roomList });
  }


});

// 工具函数
function sendMessage(ws, type, data) {
  if (ws.readyState === WebSocket.OPEN) {
    ws.send(JSON.stringify({ type, data }));
  }
}

function sendError(ws, message) {
  sendMessage(ws, 'error', { message });
}

function broadcastToRoom(roomId, type, data, excludePlayerId = null) {
  const playerIds = gameData.roomPlayers.get(roomId);
  if (!playerIds) {
    console.log(`广播失败：房间 ${roomId} 没有玩家映射`);
    return;
  }

  console.log(`广播消息到房间 ${roomId}，类型: ${type}，玩家数: ${playerIds.size}`);
  console.log(`全局连接管理器中的玩家数: ${globalConnections.players.size}`);

  let sentCount = 0;
  for (const playerId of playerIds) {
    if (playerId === excludePlayerId) continue;

    const player = globalConnections.players.get(playerId);
    if (player) {
      const wsStateNames = {
        0: 'CONNECTING',
        1: 'OPEN',
        2: 'CLOSING',
        3: 'CLOSED'
      };
      const wsStateName = wsStateNames[player.ws.readyState] || 'UNKNOWN';
      console.log(`玩家 ${playerId} WebSocket状态: ${player.ws.readyState}(${wsStateName})`);

      if (player.ws.readyState === WebSocket.OPEN) {
        try {
          sendMessage(player.ws, type, data);
          sentCount++;
          console.log(`✅ 消息已发送给玩家: ${playerId}`);
        } catch (error) {
          console.log(`❌ 发送消息给玩家 ${playerId} 时出错: ${error.message}`);
        }
      } else {
        console.log(`❌ 玩家 ${playerId} WebSocket状态不是OPEN (${wsStateName})，跳过发送`);
      }
    } else {
      console.log(`❌ 玩家 ${playerId} 在全局连接管理器中不存在，跳过发送`);
    }
  }

  console.log(`广播完成，实际发送: ${sentCount}/${playerIds.size} 个玩家`);
}

function handlePlayerDisconnect(playerId, roomId) {
  if (!playerId) return;

  console.log(`玩家 ${playerId} 断线，房间: ${roomId}`);

  // 从全局连接管理器中移除玩家连接
  globalConnections.players.delete(playerId);

  // 如果在房间中，检查是否需要启动清理计时器
  if (roomId) {
    const room = gameData.rooms.get(roomId);
    if (room) {
      // 通知其他玩家该玩家掉线
      broadcastToRoom(roomId, 'playerDisconnected', {
        playerId: playerId,
        timestamp: Date.now()
      });

      // 检查是否所有玩家都掉线了
      if (checkRoomAllPlayersDisconnected(roomId)) {
        console.log(`房间 ${roomId} 所有玩家都掉线，启动清理计时器`);
        startRoomCleanupTimer(roomId);
      }
    }
  }
}

function handlePlayerLeaveRoom(roomId, playerId) {
  const room = gameData.rooms.get(roomId);
  if (!room) return;

  // 从房间玩家列表中移除
  room.players = room.players.filter(p => p.id !== playerId);
  const roomPlayerSet = gameData.roomPlayers.get(roomId);
  if (roomPlayerSet) {
    roomPlayerSet.delete(playerId);
  }

  if (room.players.length === 0) {
    // 房间为空，删除房间
    gameData.rooms.delete(roomId);
    gameData.roomPlayers.delete(roomId);
    console.log('房间已删除:', roomId);
  } else {
    // 如果离开的是房主，转移房主权限
    if (room.hostId === playerId) {
      const newHost = room.players[0];
      room.hostId = newHost.id;
      room.hostInfo = newHost;
      console.log('房主转移:', roomId, newHost.id);
    }

    room.lastUpdate = Date.now();

    // 通知房间内其他玩家
    broadcastToRoom(roomId, 'playerLeft', {
      playerId: playerId,
      players: room.players,
      newHostId: room.hostId
    });
  }
}

// 定期清理过期数据
setInterval(() => {
  const now = Date.now();
  const timeout = 5 * 60 * 1000; // 5分钟超时

  // WebSocket自带心跳检测，无需手动清理

  // 清理空房间和过期房间
  for (const [roomId, room] of gameData.rooms) {
    if (room.players.length === 0 || (now - room.lastUpdate > timeout)) {
      console.log('清理过期房间:', roomId);
      gameData.rooms.delete(roomId);
      gameData.roomPlayers.delete(roomId);
    }
  }

  // 清理过期的请求限制数据（超过1小时的记录）
  const requestTimeout = 60 * 60 * 1000; // 1小时
  for (const [key, timestamp] of gameData.requestLimits) {
    if (now - timestamp > requestTimeout) {
      gameData.requestLimits.delete(key);
    }
  }
}, 60000); // 每分钟清理一次

// HTTP API路由
app.get('/api/stats', (req, res) => {
  res.json({
    rooms: gameData.rooms.size,
    players: globalConnections.players.size,
    timestamp: Date.now()
  });
});

app.get('/api/rooms', (req, res) => {
  const roomList = [];
  for (const [roomId, room] of gameData.rooms) {
    if (room.status === 'waiting') {
      roomList.push({
        roomId: roomId,
        hostName: room.hostInfo.nickName,
        playerCount: room.players.length,
        maxPlayers: room.config.playerCount,
        createTime: room.createTime
      });
    }
  }
  res.json({ rooms: roomList });
});

// 排位赛API路由
app.get('/api/ranked/stats', (req, res) => {
  if (!matchmakingSystem) {
    return res.status(503).json({ error: '排位赛系统未初始化' });
  }

  const stats = matchmakingSystem.getQueueStatus();
  res.json({
    ...stats,
    timestamp: Date.now()
  });
});

app.get('/api/ranked/leaderboard', async (req, res) => {
  try {
    if (!databaseManager || !rankingSystem) {
      return res.status(503).json({ error: '排位赛系统未初始化' });
    }

    const limit = parseInt(req.query.limit) || 50;
    const offset = parseInt(req.query.offset) || 0;

    const leaderboard = await databaseManager.getLeaderboard(limit, offset);

    const enrichedLeaderboard = leaderboard.map((user, index) => ({
      ...user,
      rank: offset + index + 1,
      tier: rankingSystem.calculateTier(user.rating)
    }));

    res.json({
      leaderboard: enrichedLeaderboard,
      total: leaderboard.length,
      timestamp: Date.now()
    });

  } catch (error) {
    console.error('获取排行榜API失败:', error);
    res.status(500).json({ error: '获取排行榜失败' });
  }
});

// 启动服务器
const PORT = process.env.PORT || 3000;
server.listen(PORT, async () => {
  console.log(`🎮 花砖物语游戏服务器启动成功！`);
  console.log(`HTTP服务器: http://localhost:${PORT}`);
  console.log(`WebSocket服务器: ws://localhost:${PORT}`);

  // 初始化排位赛系统
  await initRankedSystems();
});

// 查找玩家所在的房间（全局函数）
function findPlayerRoom(playerId) {
  for (let [roomId, room] of gameData.rooms) {
    if (room.players.some(player => player.id === playerId)) {
      return roomId;
    }
  }
  return null;
}

// 检查房间是否所有玩家都掉线（全局函数）
function checkRoomAllPlayersDisconnected(roomId) {
  const room = gameData.rooms.get(roomId);
  if (!room) {
    return false;
  }

  // 检查房间内是否有任何在线玩家
  let hasOnlinePlayer = false;
  for (let player of room.players) {
    const playerConnection = globalConnections.players.get(player.id);
    if (playerConnection && playerConnection.ws.readyState === WebSocket.OPEN) {
      hasOnlinePlayer = true;
      break;
    }
  }

  return !hasOnlinePlayer;
}

// 启动房间清理计时器（全局函数）
function startRoomCleanupTimer(roomId) {
  console.log(`启动房间清理计时器: ${roomId}`);

  // 清除之前的计时器
  clearRoomCleanupTimer(roomId);

  // 设置2分钟后清理房间
  const timer = setTimeout(() => {
    console.log(`房间清理计时器到期: ${roomId}`);

    // 再次检查是否所有玩家都掉线
    if (checkRoomAllPlayersDisconnected(roomId)) {
      console.log(`房间 ${roomId} 所有玩家掉线超过2分钟，开始清理`);
      cleanupRoom(roomId, '所有玩家掉线超过2分钟');
    } else {
      console.log(`房间 ${roomId} 有玩家重新连接，取消清理`);
    }
  }, 2 * 60 * 1000); // 2分钟

  gameData.roomCleanupTimers.set(roomId, timer);
}

// 清除房间清理计时器（全局函数）
function clearRoomCleanupTimer(roomId) {
  const timer = gameData.roomCleanupTimers.get(roomId);
  if (timer) {
    clearTimeout(timer);
    gameData.roomCleanupTimers.delete(roomId);
    console.log(`清除房间清理计时器: ${roomId}`);
  }
}

// 停止回合计时器（全局函数）
function stopTurnTimer(roomId) {
  console.log(`停止回合计时器，房间: ${roomId}`);
  gameEngine.clearTurnTimer(roomId);
}

// 清理房间（全局函数）
function cleanupRoom(roomId, reason) {
  console.log(`清理房间 ${roomId}, 原因: ${reason}`);

  const room = gameData.rooms.get(roomId);
  if (!room) {
    console.log(`房间 ${roomId} 不存在，跳过清理`);
    return;
  }

  // 检查房间状态，如果游戏仍在进行中，不要清理
  if (room.status === 'playing' && room.gameState && !room.gameState.gameEnded) {
    console.log(`房间 ${roomId} 游戏仍在进行中，跳过清理。状态: ${room.status}, 游戏结束: ${room.gameState.gameEnded}`);
    return;
  }

  console.log(`确认清理房间 ${roomId}, 状态: ${room.status}, 游戏结束: ${room.gameState?.gameEnded}`);

  // 停止游戏计时器
  stopTurnTimer(roomId);

  // 清除房间清理计时器
  clearRoomCleanupTimer(roomId);

  // 通知剩余在线玩家（如果有的话）
  const roomPlayerIds = gameData.roomPlayers.get(roomId);
  if (roomPlayerIds && roomPlayerIds.size > 0) {
    // 检查是否有在线玩家
    let hasOnlinePlayers = false;
    for (const playerId of roomPlayerIds) {
      const playerConnection = globalConnections.players.get(playerId);
      if (playerConnection && playerConnection.ws.readyState === WebSocket.OPEN) {
        hasOnlinePlayers = true;
        break;
      }
    }

    if (hasOnlinePlayers) {
      console.log(`通知房间 ${roomId} 的在线玩家房间关闭`);
      broadcastToRoom(roomId, 'roomClosed', {
        roomId: roomId,
        reason: reason,
        timestamp: Date.now()
      });
    } else {
      console.log(`房间 ${roomId} 没有在线玩家，跳过房间关闭通知`);
    }
  }

  // 房间清理不再删除玩家连接
  // 玩家连接现在由全局连接管理器管理，与房间生命周期无关
  console.log(`房间 ${roomId} 清理时保留所有玩家连接（连接由全局管理器管理）`);

  // 清理房间-玩家映射
  gameData.roomPlayers.delete(roomId);

  // 删除房间
  gameData.rooms.delete(roomId);

  console.log(`房间 ${roomId} 清理完成`);
}

// 获取剩余时间（全局函数）
function getRemainingTime(roomId) {
  return gameEngine.getRemainingTime(roomId);
}

// 处理获取剩余时间请求（全局函数）
function handleGetRemainingTime(ws, data) {
  console.log('收到获取剩余时间请求:', data);
  const { roomId } = data;
  const room = gameData.rooms.get(roomId);

  if (!room) {
    console.log('房间不存在:', roomId);
    sendError(ws, '房间不存在');
    return;
  }

  const remainingTime = getRemainingTime(roomId);
  console.log('获取到剩余时间:', remainingTime);

  ws.send(JSON.stringify({
    type: 'remainingTime',
    data: {
      remainingTime: remainingTime,
      currentPlayer: room.gameState.currentPlayer,
      timestamp: Date.now()
    }
  }));
  console.log('已发送剩余时间响应');
}

// 处理计分阶段（全局函数）
function handleScoringPhase(roomId, room) {
  console.log('处理计分阶段:', roomId);

  try {
    // 先收集动画数据（在游戏引擎计分之前）
    const scoringDetails = scoringSystem.scoreRound(room.gameState);

    // 获取计分动画数据
    const animationData = scoringSystem.getScoringAnimationData(scoringDetails);

    // 广播计分结果
    const roundScoringData = {
      gameState: gameEngine.getGameStateSummary(room.gameState),
      scoringDetails: scoringDetails,
      animationData: animationData
    };

    console.log('广播回合计分事件:', {
      roomId: roomId,
      playerCount: room.players.length,
      animationDuration: animationData.duration
    });

    broadcastToRoom(roomId, 'roundScoring', roundScoringData);

    console.log('回合计分完成，等待动画播放...');

    // 等待动画播放完成后再继续
    const animationDuration = animationData.duration || 3000;
    setTimeout(() => {
      console.log('动画播放完成，完成计分阶段');

      // 完成计分阶段
      gameEngine.finishScoringPhase(room.gameState);

      // 检查游戏是否结束
      if (room.gameState.gameEnded) {
        handleGameEnd(roomId, room);
      } else {
        // 广播新轮开始
        broadcastToRoom(roomId, 'gameStateUpdate', {
          gameState: gameEngine.getGameStateSummary(room.gameState),
          message: `第${room.gameState.round}轮开始`
        });

        // 启动新轮的计时器
        startTurnTimer(roomId, room);
      }
    }, animationDuration);

  } catch (error) {
    console.error('计分阶段处理失败:', error);
    broadcastToRoom(roomId, 'error', {
      message: '计分失败: ' + error.message
    });
  }
}

// 处理游戏结束（全局函数）
function handleGameEnd(roomId, room) {
  console.log('处理游戏结束:', roomId);

  try {
    // 执行最终计分
    const finalScoringDetails = scoringSystem.finalScoring(room.gameState);

    // 获取最终计分动画数据
    const animationData = scoringSystem.getFinalScoringAnimationData(finalScoringDetails);

    // 更新房间状态
    room.status = 'finished';
    room.endTime = Date.now();

    // 广播游戏结束
    broadcastToRoom(roomId, 'gameEnded', {
      gameState: gameEngine.getGameStateSummary(room.gameState),
      finalScoringDetails: finalScoringDetails,
      animationData: animationData,
      winner: room.gameState.winner,
      gameStats: {
        duration: room.endTime - room.startTime,
        rounds: room.gameState.round,
        totalMoves: room.gameState.moveHistory.length
      }
    });

    console.log('游戏结束处理完成，获胜者:', room.gameState.winner);

    // 检查是否为排位赛房间
    if (roomId.startsWith('ranked_') && matchmakingSystem) {
      console.log('处理排位赛结束，计算积分变化');

      // 构建游戏结果数据
      const gameResult = {
        finalScores: room.gameState.players.map(p => p.score),
        winner: room.gameState.winner,
        duration: room.endTime - room.startTime,
        rounds: room.gameState.round,
        totalMoves: room.gameState.moveHistory?.length || 0
      };

      // 调用排位赛完成处理
      matchmakingSystem.completeRankedMatch(roomId, gameResult).catch(error => {
        console.error('排位赛完成处理失败:', error);
      });
    }

    // 停止回合计时器
    stopTurnTimer(roomId);

    // 清除房间清理计时器（如果有的话）
    clearRoomCleanupTimer(roomId);

    // 设置房间清理计时器，排位赛房间3分钟后清理，普通房间5分钟后清理
    const cleanupDelay = roomId.startsWith('ranked_') ? 3 * 60 * 1000 : 5 * 60 * 1000;
    const cleanupTimer = setTimeout(() => {
      console.log(`自动清理已结束的房间: ${roomId}`);
      cleanupRoom(roomId, '游戏结束后自动清理');
    }, cleanupDelay);

    // 保存定时器，以便后续可以清除
    gameData.roomCleanupTimers.set(roomId, cleanupTimer);

  } catch (error) {
    console.error('游戏结束处理失败:', error);
    broadcastToRoom(roomId, 'error', {
      message: '游戏结束处理失败: ' + error.message
    });
  }
}

// 启动回合计时器（全局函数）
function startTurnTimer(roomId, room) {
  console.log(`启动回合计时器，房间: ${roomId}`);

  // 添加备用超时检查
  const backupTimer = setTimeout(() => {
    console.log(`备用超时检查触发，房间: ${roomId}`);
    const currentRoom = gameData.rooms.get(roomId);
    if (currentRoom && currentRoom.gameState && !currentRoom.gameState.gameEnded) {
      console.log(`检测到可能的超时问题，强制处理超时，房间: ${roomId}`);
      // 手动触发超时处理
      const aiMove = gameEngine.generateAIMove(currentRoom.gameState);
      if (aiMove) {
        const result = gameEngine.executeMove(currentRoom.gameState, currentRoom.gameState.players[currentRoom.gameState.currentPlayer].id, aiMove);
        if (result.success) {
          // 手动调用超时处理逻辑
          handleTimeoutCallback(roomId, currentRoom, result.gameState, aiMove);
        }
      }
    }
  }, gameEngine.turnTimeLimit + 5000); // 比正常超时多等5秒

  gameEngine.startTurnTimer(roomId, room.gameState, (newGameState, aiMove) => {
    // 清除备用计时器
    clearTimeout(backupTimer);
    console.log('AI代替超时玩家执行动作:', aiMove);

    // 使用通用的超时处理函数
    handleTimeoutCallback(roomId, room, newGameState, aiMove);
  });
}

// 处理超时回调的通用函数
function handleTimeoutCallback(roomId, room, newGameState, aiMove) {
  console.log('处理超时回调，房间:', roomId);

  // 获取超时玩家信息（在更新游戏状态之前获取）
  const timeoutPlayerId = room.gameState.players[room.gameState.currentPlayer].id;

  // 更新房间状态
  room.gameState = newGameState;
  room.lastUpdate = Date.now();

  // 广播超时和AI动作
  broadcastToRoom(roomId, 'playerTimeout', {
    timeoutPlayerId: timeoutPlayerId,
    aiMove: aiMove,
    gameState: gameEngine.getGameStateSummary(newGameState),
    timestamp: Date.now()
  });

  // 根据游戏阶段继续处理
  if (newGameState.phase === 'scoring') {
    handleScoringPhase(roomId, room);
  } else if (newGameState.phase === 'end') {
    handleGameEnd(roomId, room);
  } else {
    // 正常游戏继续，启动下一个玩家的计时器
    // 注意：不需要再发送gameStateUpdate，因为playerTimeout消息已经包含了游戏状态
    console.log('正常游戏继续，启动下一个玩家的计时器');

    // 启动下一个玩家的计时器
    startTurnTimer(roomId, room);
  }
}

// 移除自定义心跳检查机制，使用WebSocket自带的心跳

console.log('🔧 房间自动清理机制已启动 - 使用WebSocket自带心跳检测');

// ==================== 排位赛系统处理函数 ====================

// 处理加入排位队列
async function handleJoinRankedQueue(ws, data) {
  try {
    if (!matchmakingSystem) {
      sendError(ws, '排位赛系统未初始化');
      return;
    }

    const { playerId, playerInfo } = data;

    if (!playerId) {
      sendError(ws, '玩家未认证');
      return;
    }

    console.log(`玩家 ${playerInfo.nickName} 加入排位队列`);

    // 确保玩家连接被添加到全局连接管理器
    const existingPlayer = globalConnections.players.get(playerId);
    if (existingPlayer && existingPlayer.ws !== ws) {
      console.log('检测到重复连接，关闭旧连接:', playerId);
      if (existingPlayer.ws && existingPlayer.ws.readyState === WebSocket.OPEN) {
        existingPlayer.ws.close(1000, '新连接已建立');
      }
    }

    // 更新WebSocket连接状态
    ws.playerId = playerId;

    // 存储玩家连接到全局连接管理器
    globalConnections.players.set(playerId, {
      ws: ws,
      info: playerInfo,
      connectTime: Date.now()
    });

    console.log(`排位赛玩家连接已添加到全局管理器: ${playerId}, 当前在线玩家数: ${globalConnections.players.size}`);

    await matchmakingSystem.joinQueue(playerId, playerInfo, ws);

  } catch (error) {
    console.error('加入排位队列失败:', error);

    // 如果是重复加入队列的错误，发送成功响应而不是错误
    if (error.message.includes('玩家已在匹配队列中')) {
      console.log(`玩家 ${data.playerId} 重复加入队列请求，忽略`);
      // 发送成功响应，避免客户端显示错误
      ws.send(JSON.stringify({
        type: 'joinRankedQueueSuccess',
        data: { message: '已在匹配队列中' }
      }));
    } else {
      sendError(ws, '加入排位队列失败: ' + error.message);
    }
  }
}

// 处理离开排位队列
function handleLeaveRankedQueue(ws, data) {
  try {
    if (!matchmakingSystem) {
      sendError(ws, '排位赛系统未初始化');
      return;
    }

    const { playerId } = data;

    if (!playerId) {
      sendError(ws, '玩家未认证');
      return;
    }

    console.log(`玩家 ${playerId} 离开排位队列`);

    const success = matchmakingSystem.leaveQueue(playerId);

    if (success) {
      sendMessage(ws, 'queueLeft', { success: true });
    } else {
      sendError(ws, '玩家不在队列中');
    }

  } catch (error) {
    console.error('离开排位队列失败:', error);
    sendError(ws, '离开排位队列失败: ' + error.message);
  }
}

// 处理获取用户排位信息
async function handleGetUserRanking(ws, data) {
  try {
    if (!databaseManager || !rankingSystem) {
      sendError(ws, '排位赛系统未初始化');
      return;
    }

    const { playerId } = data;

    if (!playerId) {
      sendError(ws, '玩家未认证');
      return;
    }

    // 获取用户数据
    const user = await databaseManager.getOrCreateUser(playerId, { nickName: '玩家' });
    const rank = await databaseManager.getUserRank(playerId);
    const tierInfo = rankingSystem.calculateTier(user.rating);

    sendMessage(ws, 'userRanking', {
      rating: user.rating,
      tier: tierInfo,
      rank: rank,
      totalGames: user.total_games,
      wins: user.wins,
      losses: user.losses,
      winRate: user.win_rate,
      currentStreak: user.current_streak,
      highestRating: user.highest_rating
    });

  } catch (error) {
    console.error('获取用户排位信息失败:', error);
    sendError(ws, '获取排位信息失败: ' + error.message);
  }
}

// 处理获取排行榜
async function handleGetLeaderboard(ws, data) {
  try {
    if (!databaseManager || !rankingSystem) {
      sendError(ws, '排位赛系统未初始化');
      return;
    }

    const { limit = 50, offset = 0 } = data;

    const leaderboard = await databaseManager.getLeaderboard(limit, offset);

    // 添加段位信息
    const enrichedLeaderboard = leaderboard.map((user, index) => ({
      ...user,
      rank: offset + index + 1,
      tier: rankingSystem.calculateTier(user.rating)
    }));

    sendMessage(ws, 'leaderboard', {
      leaderboard: enrichedLeaderboard,
      total: leaderboard.length
    });

  } catch (error) {
    console.error('获取排行榜失败:', error);
    sendError(ws, '获取排行榜失败: ' + error.message);
  }
}

// 处理获取用户历史记录
async function handleGetUserHistory(ws, data) {
  try {
    if (!databaseManager) {
      sendError(ws, '排位赛系统未初始化');
      return;
    }

    const { playerId, limit = 20 } = data;

    if (!playerId) {
      sendError(ws, '玩家未认证');
      return;
    }

    const history = await databaseManager.getUserHistory(playerId, limit);

    sendMessage(ws, 'userHistory', {
      history: history
    });

  } catch (error) {
    console.error('获取用户历史记录失败:', error);
    sendError(ws, '获取历史记录失败: ' + error.message);
  }
}
