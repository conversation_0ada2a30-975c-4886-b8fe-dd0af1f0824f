<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>花砖物语排位赛测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #1a1a2e;
            color: #ffffff;
        }
        .container {
            background-color: #2d2d44;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #45a049;
        }
        button:disabled {
            background-color: #666;
            cursor: not-allowed;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            background-color: #333;
        }
        .success { background-color: #4CAF50; }
        .error { background-color: #f44336; }
        .warning { background-color: #ff9800; }
        .info { background-color: #2196F3; }
        
        .user-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin: 10px 0;
        }
        .user-card {
            background-color: #3d3d5a;
            padding: 15px;
            border-radius: 8px;
        }
        .tier-icon {
            font-size: 24px;
            margin-right: 10px;
        }
        .leaderboard {
            max-height: 400px;
            overflow-y: auto;
        }
        .leaderboard-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            margin: 5px 0;
            background-color: #3d3d5a;
            border-radius: 5px;
        }
        .rank {
            font-weight: bold;
            color: #FFD700;
        }
        .progress-bar {
            width: 100%;
            height: 10px;
            background-color: #333;
            border-radius: 5px;
            overflow: hidden;
            margin: 5px 0;
        }
        .progress-fill {
            height: 100%;
            background-color: #4CAF50;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <h1>🎮 花砖物语排位赛测试</h1>
    
    <div class="container">
        <h2>连接状态</h2>
        <div id="connectionStatus" class="status">未连接</div>
        <button id="connectBtn" onclick="connect()">连接服务器</button>
        <button id="disconnectBtn" onclick="disconnect()" disabled>断开连接</button>
    </div>

    <div class="container">
        <h2>用户信息</h2>
        <div id="userInfo" class="user-info">
            <div class="user-card">
                <div>昵称: <span id="nickname">未登录</span></div>
                <div>积分: <span id="rating">-</span></div>
                <div>段位: <span id="tier">-</span></div>
                <div>排名: <span id="rank">-</span></div>
            </div>
            <div class="user-card">
                <div>总场次: <span id="totalGames">-</span></div>
                <div>胜率: <span id="winRate">-</span></div>
                <div>连胜: <span id="streak">-</span></div>
                <div>最高积分: <span id="highestRating">-</span></div>
            </div>
        </div>
        <button id="refreshUserBtn" onclick="refreshUserInfo()" disabled>刷新用户信息</button>
    </div>

    <div class="container">
        <h2>排位匹配</h2>
        <div id="queueStatus" class="status" style="display: none;">
            <div>队列位置: <span id="queuePosition">-</span></div>
            <div>等待时间: <span id="waitTime">-</span>秒</div>
            <div>预计等待: <span id="estimatedTime">-</span>秒</div>
        </div>
        <button id="joinQueueBtn" onclick="joinQueue()" disabled>开始匹配</button>
        <button id="leaveQueueBtn" onclick="leaveQueue()" disabled style="display: none;">取消匹配</button>
    </div>

    <div class="container">
        <h2>排行榜</h2>
        <div id="leaderboard" class="leaderboard"></div>
        <button id="refreshLeaderboardBtn" onclick="refreshLeaderboard()" disabled>刷新排行榜</button>
    </div>

    <div class="container">
        <h2>日志</h2>
        <div id="log" style="height: 200px; overflow-y: auto; background-color: #000; padding: 10px; font-family: monospace; font-size: 12px;"></div>
        <button onclick="clearLog()">清空日志</button>
    </div>

    <script>
        let socket = null;
        let connected = false;
        let playerId = 'test_player_' + Math.random().toString(36).substr(2, 9);
        let playerInfo = {
            nickName: '测试玩家' + Math.floor(Math.random() * 1000),
            avatarUrl: ''
        };

        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : 'info';
            logDiv.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        function updateConnectionStatus(status, type = 'info') {
            const statusDiv = document.getElementById('connectionStatus');
            statusDiv.textContent = status;
            statusDiv.className = `status ${type}`;
        }

        function updateButtons() {
            document.getElementById('connectBtn').disabled = connected;
            document.getElementById('disconnectBtn').disabled = !connected;
            document.getElementById('refreshUserBtn').disabled = !connected;
            document.getElementById('joinQueueBtn').disabled = !connected;
            document.getElementById('refreshLeaderboardBtn').disabled = !connected;
        }

        function connect() {
            if (socket) {
                socket.close();
            }

            socket = new WebSocket('ws://localhost:3000', ['game-protocol']);
            
            socket.onopen = () => {
                connected = true;
                updateConnectionStatus('已连接', 'success');
                updateButtons();
                log('WebSocket连接成功', 'success');
                
                // 发送认证信息
                sendMessage('auth', {
                    playerId: playerId,
                    playerInfo: playerInfo
                });
            };

            socket.onmessage = (event) => {
                try {
                    const message = JSON.parse(event.data);
                    handleMessage(message);
                } catch (error) {
                    log('解析消息失败: ' + error.message, 'error');
                }
            };

            socket.onclose = () => {
                connected = false;
                updateConnectionStatus('连接已断开', 'warning');
                updateButtons();
                log('WebSocket连接关闭', 'warning');
            };

            socket.onerror = (error) => {
                log('WebSocket错误: ' + error.message, 'error');
            };
        }

        function disconnect() {
            if (socket) {
                socket.close();
            }
        }

        function sendMessage(type, data) {
            if (!socket || socket.readyState !== WebSocket.OPEN) {
                log('WebSocket未连接', 'error');
                return;
            }

            const message = { type, data };
            socket.send(JSON.stringify(message));
            log(`发送消息: ${type}`, 'info');
        }

        function handleMessage(message) {
            log(`收到消息: ${message.type}`, 'info');
            
            switch (message.type) {
                case 'authSuccess':
                    log('认证成功', 'success');
                    refreshUserInfo();
                    break;
                    
                case 'userRanking':
                    updateUserInfo(message.data);
                    break;
                    
                case 'queueJoined':
                    showQueueStatus(message.data);
                    break;
                    
                case 'queueUpdate':
                    updateQueueStatus(message.data);
                    break;
                    
                case 'queueLeft':
                    hideQueueStatus();
                    break;
                    
                case 'matchFound':
                    log('匹配成功！', 'success');
                    hideQueueStatus();
                    break;
                    
                case 'leaderboard':
                    updateLeaderboard(message.data);
                    break;
                    
                case 'error':
                    log('服务器错误: ' + message.data.message, 'error');
                    break;
                    
                default:
                    log(`未处理的消息类型: ${message.type}`, 'warning');
            }
        }

        function refreshUserInfo() {
            sendMessage('getUserRanking', { playerId: playerId });
        }

        function updateUserInfo(data) {
            document.getElementById('nickname').textContent = playerInfo.nickName;
            document.getElementById('rating').textContent = data.rating || '-';
            document.getElementById('rank').textContent = data.rank ? `#${data.rank}` : '-';
            document.getElementById('totalGames').textContent = data.totalGames || 0;
            document.getElementById('winRate').textContent = data.winRate ? (data.winRate * 100).toFixed(1) + '%' : '-';
            document.getElementById('streak').textContent = data.currentStreak || 0;
            document.getElementById('highestRating').textContent = data.highestRating || '-';
            
            if (data.tier) {
                document.getElementById('tier').innerHTML = `${data.tier.icon || ''} ${data.tier.name || '-'}`;
            }
        }

        function joinQueue() {
            sendMessage('joinRankedQueue', {
                playerId: playerId,
                playerInfo: playerInfo
            });
        }

        function leaveQueue() {
            sendMessage('leaveRankedQueue', { playerId: playerId });
        }

        function showQueueStatus(data) {
            const queueDiv = document.getElementById('queueStatus');
            queueDiv.style.display = 'block';
            updateQueueStatus(data);
            
            document.getElementById('joinQueueBtn').style.display = 'none';
            document.getElementById('leaveQueueBtn').style.display = 'inline-block';
            document.getElementById('leaveQueueBtn').disabled = false;
        }

        function updateQueueStatus(data) {
            document.getElementById('queuePosition').textContent = data.position || '-';
            document.getElementById('waitTime').textContent = data.waitTime || 0;
            document.getElementById('estimatedTime').textContent = data.estimatedWaitTime || '-';
        }

        function hideQueueStatus() {
            document.getElementById('queueStatus').style.display = 'none';
            document.getElementById('joinQueueBtn').style.display = 'inline-block';
            document.getElementById('leaveQueueBtn').style.display = 'none';
        }

        function refreshLeaderboard() {
            sendMessage('getLeaderboard', { limit: 20, offset: 0 });
        }

        function updateLeaderboard(data) {
            const leaderboardDiv = document.getElementById('leaderboard');
            leaderboardDiv.innerHTML = '';
            
            if (data.leaderboard && data.leaderboard.length > 0) {
                data.leaderboard.forEach(player => {
                    const item = document.createElement('div');
                    item.className = 'leaderboard-item';
                    
                    const tierIcon = player.tier ? player.tier.icon : '🏆';
                    const tierName = player.tier ? player.tier.name : 'Bronze 5';
                    const winRate = (player.win_rate * 100).toFixed(1);
                    
                    item.innerHTML = `
                        <div>
                            <span class="rank">#${player.rank}</span>
                            <span>${player.nickname}</span>
                        </div>
                        <div>
                            <span class="tier-icon">${tierIcon}</span>
                            <span>${tierName}</span>
                        </div>
                        <div>
                            <div>${player.rating}分</div>
                            <div>${winRate}% (${player.total_games}场)</div>
                        </div>
                    `;
                    
                    leaderboardDiv.appendChild(item);
                });
            } else {
                leaderboardDiv.innerHTML = '<div class="status">暂无排行榜数据</div>';
            }
        }

        // 页面加载完成后自动连接
        window.onload = () => {
            log('页面加载完成', 'info');
            updateButtons();
        };
    </script>
</body>
</html>
