// 花砖物语游戏引擎 - 服务端核心逻辑
class AzulGameEngine {
  constructor() {
    this.colors = ['blue', 'yellow', 'red', 'black', 'white'];
    this.floorPenalties = [-1, -1, -2, -2, -2, -3, -3]; // 地板线惩罚分数
    this.turnTimeLimit = 5000; // 20秒回合时间限制
    this.turnTimers = new Map(); // 存储每个房间的计时器
  }

  // 创建新游戏
  createGame(players) {
    console.log('创建新游戏，玩家数量:', players.length);
    
    const playerCount = players.length;
    const factoryCount = this.getFactoryCount(playerCount);
    
    const gameState = {
      // 基本信息
      playerCount: playerCount,
      currentPlayer: 0,
      round: 1,
      phase: 'collect', // collect, scoring, end
      gameEnded: false,
      winner: null,
      
      // 玩家状态
      players: players.map((player, index) => ({
        id: player.id,
        name: player.nickName || player.name || `玩家${index + 1}`,
        nickName: player.nickName || player.name || `玩家${index + 1}`,
        index: index,
        score: 0,
        patternLines: [[], [], [], [], []], // 5行准备区
        wall: this.createWall(),
        floorLine: [],
        hasFirstPlayerTile: false
      })),
      
      // 游戏组件
      factories: Array(factoryCount).fill(null).map(() => []),
      centerArea: [],
      firstPlayerMarker: true, // 首个玩家标记是否在中央区域
      
      // 瓷砖管理
      tileBag: this.createTileBag(),
      discardPile: [],
      
      // 游戏历史（用于调试和回放）
      moveHistory: [],
      lastUpdate: Date.now()
    };
    
    // 填充工厂
    this.fillFactories(gameState);
    
    console.log('游戏创建完成，工厂数量:', factoryCount);
    return gameState;
  }

  // 获取工厂数量
  getFactoryCount(playerCount) {
    const factoryCounts = {
      2: 5,
      3: 7,
      4: 9
    };
    return factoryCounts[playerCount] || 5;
  }

  // 创建墙壁
  createWall() {
    const colorPattern = [
      ['blue', 'yellow', 'red', 'black', 'teal'],
      ['teal', 'blue', 'yellow', 'red', 'black'],
      ['black', 'teal', 'blue', 'yellow', 'red'],
      ['red', 'black', 'teal', 'blue', 'yellow'],
      ['yellow', 'red', 'black', 'teal', 'blue']
    ];

    return colorPattern.map(row =>
      row.map(color => ({ color, filled: false }))
    );
  }

  // 创建瓷砖袋
  createTileBag() {
    const tileBag = [];
    
    // 每种颜色20个瓷砖
    this.colors.forEach(color => {
      for (let i = 0; i < 20; i++) {
        tileBag.push(color);
      }
    });
    
    // 打乱瓷砖袋
    for (let i = tileBag.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [tileBag[i], tileBag[j]] = [tileBag[j], tileBag[i]];
    }
    
    return tileBag;
  }

  // 填充工厂
  fillFactories(gameState) {
    console.log('填充工厂，瓷砖袋剩余:', gameState.tileBag.length);
    
    for (let i = 0; i < gameState.factories.length; i++) {
      gameState.factories[i] = [];
      
      // 每个工厂放4个瓷砖
      for (let j = 0; j < 4; j++) {
        if (gameState.tileBag.length > 0) {
          const randomIndex = Math.floor(Math.random() * gameState.tileBag.length);
          const tile = gameState.tileBag.splice(randomIndex, 1)[0];
          gameState.factories[i].push(tile);
        } else {
          // 瓷砖袋空了，从弃牌堆重新洗牌
          this.refillTileBag(gameState);
          if (gameState.tileBag.length > 0) {
            const randomIndex = Math.floor(Math.random() * gameState.tileBag.length);
            const tile = gameState.tileBag.splice(randomIndex, 1)[0];
            gameState.factories[i].push(tile);
          }
        }
      }
      
      console.log(`工厂${i}:`, gameState.factories[i]);
    }
  }

  // 重新填充瓷砖袋
  refillTileBag(gameState) {
    console.log('瓷砖袋空了，从弃牌堆重新洗牌');
    gameState.tileBag = [...gameState.discardPile];
    gameState.discardPile = [];
    
    // 打乱瓷砖袋
    for (let i = gameState.tileBag.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [gameState.tileBag[i], gameState.tileBag[j]] = [gameState.tileBag[j], gameState.tileBag[i]];
    }
    
    console.log('重新洗牌完成，瓷砖袋大小:', gameState.tileBag.length);
  }

  // 验证玩家动作
  validateMove(gameState, playerId, move) {
    console.log('验证动作:', playerId, move);

    // 调试：显示所有工厂的状态
    console.log('当前所有工厂状态:');
    gameState.factories.forEach((factory, index) => {
      console.log(`工厂${index}:`, factory, 'tiles:', factory.tiles, 'isArray:', Array.isArray(factory.tiles || factory));
    });
    
    // 检查基本条件
    if (gameState.gameEnded) {
      return { valid: false, error: '游戏已结束' };
    }
    
    if (gameState.phase !== 'collect') {
      return { valid: false, error: '当前不是收集阶段' };
    }
    
    // 检查是否轮到该玩家
    const currentPlayer = gameState.players[gameState.currentPlayer];
    console.log('回合检查:', {
      currentPlayerIndex: gameState.currentPlayer,
      currentPlayerId: currentPlayer.id,
      requestPlayerId: playerId,
      isMatch: currentPlayer.id === playerId
    });

    if (currentPlayer.id !== playerId) {
      return { valid: false, error: '不是你的回合' };
    }
    
    // 根据动作类型验证
    switch (move.type) {
      case 'selectFromFactory':
        return this.validateFactorySelection(gameState, move);
      case 'selectFromCenter':
        return this.validateCenterSelection(gameState, move);
      default:
        return { valid: false, error: '未知的动作类型' };
    }
  }

  // 验证工厂选择
  validateFactorySelection(gameState, move) {
    const { factoryIndex, color, lineIndex } = move;
    
    // 检查工厂索引
    if (factoryIndex < 0 || factoryIndex >= gameState.factories.length) {
      return { valid: false, error: '无效的工厂索引' };
    }
    
    const factory = gameState.factories[factoryIndex];
    const factoryTiles = factory.tiles || factory;

    console.log(`验证工厂${factoryIndex}:`, {
      factory: factory,
      factoryTiles: factoryTiles,
      isArray: Array.isArray(factoryTiles),
      type: typeof factoryTiles
    });

    // 检查工厂数据格式
    if (!Array.isArray(factoryTiles)) {
      console.log(`工厂${factoryIndex}数据格式错误:`, factoryTiles);
      return { valid: false, error: '工厂状态无效' };
    }

    // 检查工厂是否为空
    if (factoryTiles.length === 0) {
      return { valid: false, error: '工厂已空' };
    }

    // 检查颜色是否存在
    if (!factoryTiles.includes(color)) {
      return { valid: false, error: '工厂中没有该颜色的瓷砖' };
    }
    
    // 验证放置位置
    return this.validatePlacement(gameState, color, lineIndex);
  }

  // 验证中央区域选择
  validateCenterSelection(gameState, move) {
    const { color, lineIndex } = move;

    const centerTiles = gameState.centerArea.tiles || gameState.centerArea;

    // 检查中央区域数据格式
    if (!Array.isArray(centerTiles)) {
      return { valid: false, error: '中央区域状态无效' };
    }

    // 检查中央区域是否为空
    if (centerTiles.length === 0) {
      return { valid: false, error: '中央区域为空' };
    }

    // 检查颜色是否存在
    if (!centerTiles.includes(color)) {
      return { valid: false, error: '中央区域没有该颜色的瓷砖' };
    }
    
    // 验证放置位置
    return this.validatePlacement(gameState, color, lineIndex);
  }

  // 验证瓷砖放置
  validatePlacement(gameState, color, lineIndex) {
    const currentPlayer = gameState.players[gameState.currentPlayer];
    
    // 检查是否放置到地板线
    if (lineIndex === 5) {
      return { valid: true }; // 总是可以放到地板线
    }
    
    // 检查行索引
    if (lineIndex < 0 || lineIndex > 4) {
      return { valid: false, error: '无效的行索引' };
    }
    
    const patternLine = currentPlayer.patternLines[lineIndex];
    const maxCapacity = lineIndex + 1;
    
    // 检查准备区是否已满
    if (patternLine.length >= maxCapacity) {
      return { valid: true }; // 可以放置，多余的会进入地板线
    }
    
    // 检查颜色是否匹配
    if (patternLine.length > 0 && patternLine[0] !== color) {
      return { valid: true }; // 可以放置，但会进入地板线
    }
    
    // 检查墙壁是否已有该颜色
    const wallRow = currentPlayer.wall[lineIndex];
    const wallHasColor = wallRow.some(cell => cell.color === color && cell.filled);
    
    if (wallHasColor) {
      return { valid: true }; // 可以放置，但会进入地板线
    }
    
    return { valid: true };
  }

  // 执行玩家动作
  executeMove(gameState, playerId, move) {
    console.log('执行动作:', playerId, move);
    
    // 验证动作
    const validation = this.validateMove(gameState, playerId, move);
    if (!validation.valid) {
      return { success: false, error: validation.error };
    }
    
    // 记录动作历史
    gameState.moveHistory.push({
      playerId: playerId,
      move: move,
      timestamp: Date.now()
    });
    
    // 执行动作
    let selectedTiles = [];
    
    if (move.type === 'selectFromFactory') {
      selectedTiles = this.executeFactorySelection(gameState, move);
    } else if (move.type === 'selectFromCenter') {
      selectedTiles = this.executeCenterSelection(gameState, move);
    }
    
    // 放置瓷砖
    this.placeTiles(gameState, selectedTiles, move.lineIndex);
    
    // 检查回合是否结束
    if (this.isRoundComplete(gameState)) {
      this.startScoringPhase(gameState);
    } else {
      // 切换到下一个玩家
      this.nextPlayer(gameState);
    }
    
    gameState.lastUpdate = Date.now();
    
    return { success: true, gameState: gameState };
  }

  // 执行工厂选择
  executeFactorySelection(gameState, move) {
    const { factoryIndex, color } = move;
    const factory = gameState.factories[factoryIndex];
    const factoryTiles = factory.tiles || factory; // 兼容新旧格式

    if (!Array.isArray(factoryTiles)) {
      throw new Error(`工厂${factoryIndex}数据格式错误`);
    }

    // 获取选中的瓷砖
    const selectedTiles = factoryTiles.filter(tile => tile === color);
    const remainingTiles = factoryTiles.filter(tile => tile !== color);

    console.log(`从工厂${factoryIndex}选择${selectedTiles.length}个${color}瓷砖`);

    // 清空工厂
    if (factory.tiles) {
      factory.tiles = []; // 新格式
    } else {
      gameState.factories[factoryIndex] = []; // 旧格式
    }

    // 剩余瓷砖放入中央区域
    const centerTiles = gameState.centerArea.tiles || gameState.centerArea;
    if (Array.isArray(centerTiles)) {
      centerTiles.push(...remainingTiles);
    } else {
      gameState.centerArea.push(...remainingTiles);
    }

    return selectedTiles;
  }

  // 执行中央区域选择
  executeCenterSelection(gameState, move) {
    const { color } = move;

    // 兼容新旧格式
    const centerTiles = gameState.centerArea.tiles || gameState.centerArea;

    if (!Array.isArray(centerTiles)) {
      throw new Error('中央区域数据格式错误');
    }

    // 获取选中的瓷砖
    const selectedTiles = centerTiles.filter(tile => tile === color);

    console.log(`从中央区域选择${selectedTiles.length}个${color}瓷砖`);

    // 从中央区域移除选中的瓷砖
    const remainingTiles = centerTiles.filter(tile => tile !== color);
    if (gameState.centerArea.tiles) {
      gameState.centerArea.tiles = remainingTiles; // 新格式
    } else {
      gameState.centerArea = remainingTiles; // 旧格式
    }

    // 检查是否获得首个玩家标记
    if (gameState.firstPlayerMarker) {
      const currentPlayer = gameState.players[gameState.currentPlayer];
      currentPlayer.hasFirstPlayerTile = true;
      // 将起始玩家标记添加到地板线
      currentPlayer.floorLine.push('first');
      gameState.firstPlayerMarker = false;
      console.log('获得首个玩家标记，已添加到地板线');
    }

    return selectedTiles;
  }

  // 放置瓷砖
  placeTiles(gameState, tiles, lineIndex) {
    const currentPlayer = gameState.players[gameState.currentPlayer];

    console.log(`放置${tiles.length}个瓷砖到第${lineIndex + 1}行`);

    // 检查是否放置到地板线
    if (lineIndex === 5) {
      currentPlayer.floorLine.push(...tiles);
      console.log('瓷砖放置到地板线');
      return;
    }

    const patternLine = currentPlayer.patternLines[lineIndex];
    const maxCapacity = lineIndex + 1;
    const color = tiles[0];

    // 检查是否可以正常放置
    let canPlace = true;

    // 检查颜色匹配
    if (patternLine.length > 0 && patternLine[0] !== color) {
      canPlace = false;
    }

    // 检查墙壁是否已有该颜色
    const wallRow = currentPlayer.wall[lineIndex];
    const wallHasColor = wallRow.some(cell => cell.color === color && cell.filled);
    if (wallHasColor) {
      canPlace = false;
    }

    if (!canPlace) {
      // 不能放置，全部进入地板线
      currentPlayer.floorLine.push(...tiles);
      console.log('瓷砖因规则限制放入地板线');
      return;
    }

    // 正常放置
    let placedCount = 0;
    for (let tile of tiles) {
      if (patternLine.length < maxCapacity) {
        patternLine.push(tile);
        placedCount++;
      } else {
        // 溢出到地板线
        currentPlayer.floorLine.push(tile);
      }
    }

    console.log(`成功放置${placedCount}个瓷砖，${tiles.length - placedCount}个溢出到地板线`);
  }

  // 检查回合是否完成
  isRoundComplete(gameState) {
    // 检查所有工厂是否为空
    const allFactoriesEmpty = gameState.factories.every(factory => {
      const factoryTiles = factory.tiles || factory;
      return Array.isArray(factoryTiles) && factoryTiles.length === 0;
    });

    // 检查中央区域是否为空
    const centerTiles = gameState.centerArea.tiles || gameState.centerArea;
    const centerEmpty = Array.isArray(centerTiles) && centerTiles.length === 0;

    return allFactoriesEmpty && centerEmpty;
  }

  // 切换到下一个玩家
  nextPlayer(gameState) {
    gameState.currentPlayer = (gameState.currentPlayer + 1) % gameState.playerCount;
    console.log(`切换到玩家${gameState.currentPlayer}`);
  }

  // 开始计分阶段
  startScoringPhase(gameState) {
    console.log('开始计分阶段');
    gameState.phase = 'scoring';

    // 注意：不在这里执行计分，让服务端的 handleScoringPhase 来处理
    // 这样可以确保动画数据在计分之前被正确收集
  }

  // 完成计分阶段（动画播放完成后调用）
  finishScoringPhase(gameState) {
    console.log('完成计分阶段');

    // 检查游戏是否结束
    if (this.checkGameEnd(gameState)) {
      this.endGame(gameState);
    } else {
      // 准备下一轮
      this.prepareNextRound(gameState);
    }
  }

  // 回合计分
  scoreRound(gameState) {
    console.log('执行回合计分');

    for (let player of gameState.players) {
      console.log(`计算玩家${player.nickName}的分数`);

      // 移动完整的准备区到墙壁并计分
      for (let lineIndex = 0; lineIndex < 5; lineIndex++) {
        const patternLine = player.patternLines[lineIndex];
        const maxCapacity = lineIndex + 1;

        if (patternLine.length === maxCapacity) {
          // 准备区已满，移动一个瓷砖到墙壁
          const color = patternLine[0];
          const wallRow = player.wall[lineIndex];

          // 找到该颜色在墙壁中的位置
          const wallIndex = wallRow.findIndex(cell => cell.color === color);
          if (wallIndex !== -1) {
            wallRow[wallIndex].filled = true;

            // 计算分数
            const score = this.calculateTileScore(player.wall, lineIndex, wallIndex);
            player.score += score;

            console.log(`玩家${player.nickName}在第${lineIndex + 1}行放置${color}瓷砖，获得${score}分`);
          }

          // 剩余瓷砖放入弃牌堆
          gameState.discardPile.push(...patternLine.slice(1));

          // 清空准备区
          player.patternLines[lineIndex] = [];
        }
      }

      // 地板线扣分
      const floorPenalty = this.calculateFloorPenalty(player.floorLine);
      player.score += floorPenalty; // floorPenalty是负数

      if (floorPenalty < 0) {
        console.log(`玩家${player.nickName}地板线扣${Math.abs(floorPenalty)}分`);
      }

      // 确保分数不为负
      if (player.score < 0) {
        player.score = 0;
      }

      // 清空地板线（保留首个玩家标记用于下一轮）
      const hasFirstPlayerTile = player.floorLine.includes('first');
      gameState.discardPile.push(...player.floorLine.filter(tile => tile !== 'first'));
      player.floorLine = [];

      // 重置首个玩家标记状态
      player.hasFirstPlayerTile = hasFirstPlayerTile;

      console.log(`玩家${player.nickName}当前总分: ${player.score}`);
    }
  }

  // 计算单个瓷砖的分数
  calculateTileScore(wall, row, col) {
    let score = 1; // 基础分数

    // 计算水平连续瓷砖
    let horizontalCount = 1;

    // 向左计算
    for (let c = col - 1; c >= 0; c--) {
      if (wall[row][c].filled) {
        horizontalCount++;
      } else {
        break;
      }
    }

    // 向右计算
    for (let c = col + 1; c < 5; c++) {
      if (wall[row][c].filled) {
        horizontalCount++;
      } else {
        break;
      }
    }

    // 计算垂直连续瓷砖
    let verticalCount = 1;

    // 向上计算
    for (let r = row - 1; r >= 0; r--) {
      if (wall[r][col].filled) {
        verticalCount++;
      } else {
        break;
      }
    }

    // 向下计算
    for (let r = row + 1; r < 5; r++) {
      if (wall[r][col].filled) {
        verticalCount++;
      } else {
        break;
      }
    }

    // 如果有连续瓷砖，使用连续数量作为分数
    if (horizontalCount > 1) {
      score = horizontalCount;
    }
    if (verticalCount > 1) {
      score += verticalCount - 1; // 避免重复计算交叉点
    }

    return score;
  }

  // 计算地板线惩罚
  calculateFloorPenalty(floorLine) {
    let penalty = 0;
    const actualTiles = floorLine.filter(tile => tile !== 'first');

    for (let i = 0; i < actualTiles.length && i < this.floorPenalties.length; i++) {
      penalty += this.floorPenalties[i];
    }

    return penalty;
  }

  // 检查游戏是否结束
  checkGameEnd(gameState) {
    // 检查是否有玩家完成了一行
    for (let player of gameState.players) {
      for (let row of player.wall) {
        if (row.every(cell => cell.filled)) {
          console.log(`玩家${player.nickName}完成了一行，游戏结束`);
          return true;
        }
      }
    }
    return false;
  }

  // 结束游戏
  endGame(gameState) {
    console.log('游戏结束，开始最终计分');
    gameState.phase = 'end';
    gameState.gameEnded = true;

    // 注意：不在这里执行最终计分和确定获胜者
    // 让服务端的 handleGameEnd 来处理，确保使用正确的计分系统
  }

  // 最终计分
  finalScoring(gameState) {
    for (let player of gameState.players) {
      let bonusScore = 0;

      console.log(`计算玩家${player.nickName}的最终奖励分数`);

      // 完整行奖励（每行2分）
      for (let row of player.wall) {
        if (row.every(cell => cell.filled)) {
          bonusScore += 2;
          console.log(`完整行奖励: +2分`);
        }
      }

      // 完整列奖励（每列7分）
      for (let col = 0; col < 5; col++) {
        let columnComplete = true;
        for (let row = 0; row < 5; row++) {
          if (!player.wall[row][col].filled) {
            columnComplete = false;
            break;
          }
        }
        if (columnComplete) {
          bonusScore += 7;
          console.log(`完整列奖励: +7分`);
        }
      }

      // 完整颜色奖励（每种颜色5分）
      for (let color of this.colors) {
        let colorComplete = true;
        for (let row = 0; row < 5; row++) {
          const colorIndex = player.wall[row].findIndex(cell => cell.color === color);
          if (colorIndex === -1 || !player.wall[row][colorIndex].filled) {
            colorComplete = false;
            break;
          }
        }
        if (colorComplete) {
          bonusScore += 5;
          console.log(`完整颜色${color}奖励: +5分`);
        }
      }

      player.score += bonusScore;
      console.log(`玩家${player.nickName}最终奖励: ${bonusScore}分，总分: ${player.score}分`);
    }
  }

  // 确定获胜者
  determineWinner(gameState) {
    let maxScore = -1;
    let winners = [];

    // 找到最高分
    for (let player of gameState.players) {
      if (player.score > maxScore) {
        maxScore = player.score;
        winners = [player];
      } else if (player.score === maxScore) {
        winners.push(player);
      }
    }

    // 如果有平局，比较完整行数量
    if (winners.length > 1) {
      console.log('出现平局，比较完整行数量');
      let maxCompleteRows = -1;
      let finalWinners = [];

      for (let player of winners) {
        let completeRows = 0;
        for (let row of player.wall) {
          if (row.every(cell => cell.filled)) {
            completeRows++;
          }
        }

        if (completeRows > maxCompleteRows) {
          maxCompleteRows = completeRows;
          finalWinners = [player];
        } else if (completeRows === maxCompleteRows) {
          finalWinners.push(player);
        }
      }

      winners = finalWinners;
    }

    if (winners.length === 1) {
      gameState.winner = winners[0];
      console.log(`获胜者: ${winners[0].nickName}，分数: ${winners[0].score}`);
    } else {
      gameState.winner = winners;
      console.log(`平局，获胜者:`, winners.map(p => p.nickName).join(', '));
    }
  }

  // 准备下一轮
  prepareNextRound(gameState) {
    console.log('准备下一轮');

    gameState.round++;
    gameState.phase = 'collect';

    // 确定下一轮的首个玩家
    for (let i = 0; i < gameState.players.length; i++) {
      if (gameState.players[i].hasFirstPlayerTile) {
        gameState.currentPlayer = i;
        gameState.players[i].hasFirstPlayerTile = false;
        break;
      }
    }

    // 重置首个玩家标记
    gameState.firstPlayerMarker = true;

    // 填充工厂
    this.fillFactories(gameState);

    console.log(`第${gameState.round}轮开始，首个玩家: ${gameState.players[gameState.currentPlayer].name}`);
  }

  // 获取游戏状态摘要（用于客户端显示）
  getGameStateSummary(gameState) {
    return {
      playerCount: gameState.playerCount,
      currentPlayer: gameState.currentPlayer,
      round: gameState.round,
      phase: gameState.phase,
      gameEnded: gameState.gameEnded,
      winner: gameState.winner,
      players: gameState.players.map(player => ({
        id: player.id,
        name: player.name || player.nickName, // 兼容新旧格式
        nickName: player.nickName || player.name, // 兼容新旧格式
        index: player.index,
        score: player.score,
        patternLines: player.patternLines,
        wall: player.wall,
        floorLine: player.floorLine,
        hasFirstPlayerTile: player.hasFirstPlayerTile
      })),
      factories: gameState.factories,
      centerArea: gameState.centerArea,
      firstPlayerMarker: gameState.firstPlayerMarker,
      lastUpdate: gameState.lastUpdate
    };
  }

  // 获取可用动作（用于AI或提示）
  getAvailableMoves(gameState, playerId) {
    if (gameState.gameEnded || gameState.phase !== 'collect') {
      return [];
    }

    const currentPlayer = gameState.players[gameState.currentPlayer];
    if (currentPlayer.id !== playerId) {
      return [];
    }

    const moves = [];

    // 工厂选择
    for (let factoryIndex = 0; factoryIndex < gameState.factories.length; factoryIndex++) {
      const factory = gameState.factories[factoryIndex];
      const factoryTiles = factory.tiles || factory; // 兼容新旧格式
      if (Array.isArray(factoryTiles) && factoryTiles.length > 0) {
        const uniqueColors = [...new Set(factoryTiles)];
        for (let color of uniqueColors) {
          for (let lineIndex = 0; lineIndex <= 5; lineIndex++) { // 包括地板线
            moves.push({
              type: 'selectFromFactory',
              factoryIndex: factoryIndex,
              color: color,
              lineIndex: lineIndex
            });
          }
        }
      }
    }

    // 中央区域选择
    const centerTiles = gameState.centerArea.tiles || gameState.centerArea;
    if (Array.isArray(centerTiles) && centerTiles.length > 0) {
      const uniqueColors = [...new Set(centerTiles)];
      for (let color of uniqueColors) {
        for (let lineIndex = 0; lineIndex <= 5; lineIndex++) { // 包括地板线
          moves.push({
            type: 'selectFromCenter',
            color: color,
            lineIndex: lineIndex
          });
        }
      }
    }

    return moves;
  }

  // 开始回合计时器
  startTurnTimer(roomId, gameState, onTimeout) {
    console.log(`开始回合计时器，房间: ${roomId}, 当前玩家: ${gameState.currentPlayer}`);
    console.log(`计时器时长: ${this.turnTimeLimit}ms`);

    // 清除之前的计时器
    this.clearTurnTimer(roomId);

    const timer = setTimeout(() => {
      console.log(`回合超时触发，房间: ${roomId}, 玩家: ${gameState.currentPlayer}`);
      try {
        this.handleTurnTimeout(roomId, gameState, onTimeout);
      } catch (error) {
        console.error('处理回合超时时发生错误:', error);
      }
    }, this.turnTimeLimit);

    this.turnTimers.set(roomId, {
      timer: timer,
      startTime: Date.now(),
      playerId: gameState.players[gameState.currentPlayer].id
    });

    console.log(`计时器已设置，房间: ${roomId}, 玩家ID: ${gameState.players[gameState.currentPlayer].id}`);
  }

  // 清除回合计时器
  clearTurnTimer(roomId) {
    const timerInfo = this.turnTimers.get(roomId);
    if (timerInfo) {
      clearTimeout(timerInfo.timer);
      this.turnTimers.delete(roomId);
      console.log(`清除回合计时器，房间: ${roomId}`);
    }
  }

  // 处理回合超时
  handleTurnTimeout(roomId, gameState, onTimeout) {
    console.log(`处理回合超时，房间: ${roomId}`);
    console.log(`当前玩家: ${gameState.currentPlayer}, 玩家ID: ${gameState.players[gameState.currentPlayer].id}`);

    // 清除计时器
    this.clearTurnTimer(roomId);

    // 生成AI动作
    const aiMove = this.generateAIMove(gameState);
    if (aiMove) {
      console.log('AI代替超时玩家执行动作:', aiMove);

      // 执行AI动作
      const result = this.executeMove(gameState, gameState.players[gameState.currentPlayer].id, aiMove);
      if (result.success) {
        console.log('AI动作执行成功，调用超时回调');
        if (onTimeout) {
          onTimeout(result.gameState, aiMove);
        } else {
          console.error('超时回调函数为空');
        }
      } else {
        console.error('AI动作执行失败:', result.error);
      }
    } else {
      console.error('无法生成AI动作，当前游戏状态可能有问题');
      console.log('当前游戏状态:', {
        phase: gameState.phase,
        currentPlayer: gameState.currentPlayer,
        gameEnded: gameState.gameEnded
      });
    }
  }

  // 生成AI动作
  generateAIMove(gameState) {
    console.log('生成AI动作');

    const availableMoves = this.getAvailableMoves(gameState, gameState.players[gameState.currentPlayer].id);
    if (availableMoves.length === 0) {
      console.log('没有可用动作');
      return null;
    }

    // 简单AI策略：优先选择能放到准备区的动作，避免放到地板线
    let bestMove = null;
    let bestScore = -1000;

    for (let move of availableMoves) {
      let score = this.evaluateMove(gameState, move);
      if (score > bestScore) {
        bestScore = score;
        bestMove = move;
      }
    }

    console.log('AI选择动作:', bestMove, '评分:', bestScore);
    return bestMove;
  }

  // 评估动作分数
  evaluateMove(gameState, move) {
    const currentPlayer = gameState.players[gameState.currentPlayer];
    let score = 0;

    // 如果放到地板线，扣分
    if (move.lineIndex === 5) {
      score -= 10;
    } else {
      // 检查是否能正常放置到准备区
      const patternLine = currentPlayer.patternLines[move.lineIndex];
      const maxCapacity = move.lineIndex + 1;

      // 如果准备区有空间且颜色匹配，加分
      if (patternLine.length < maxCapacity) {
        if (patternLine.length === 0 || patternLine[0] === move.color) {
          // 检查墙壁是否已有该颜色
          const wallRow = currentPlayer.wall[move.lineIndex];
          const wallHasColor = wallRow.some(cell => cell.color === move.color && cell.filled);

          if (!wallHasColor) {
            score += 5; // 能正常放置，加分

            // 如果能完成准备区，额外加分
            const tilesNeeded = maxCapacity - patternLine.length;
            let availableTiles = 0;

            if (move.type === 'selectFromFactory') {
              const factory = gameState.factories[move.factoryIndex];
              const factoryTiles = factory.tiles || factory;
              availableTiles = Array.isArray(factoryTiles) ? factoryTiles.filter(tile => tile === move.color).length : 0;
            } else {
              const centerTiles = gameState.centerArea.tiles || gameState.centerArea;
              availableTiles = Array.isArray(centerTiles) ? centerTiles.filter(tile => tile === move.color).length : 0;
            }

            if (availableTiles >= tilesNeeded) {
              score += 10; // 能完成准备区，大加分
            }
          }
        }
      }
    }

    // 优先选择瓷砖数量多的
    if (move.type === 'selectFromFactory') {
      const factory = gameState.factories[move.factoryIndex];
      const factoryTiles = factory.tiles || factory;
      const tileCount = Array.isArray(factoryTiles) ? factoryTiles.filter(tile => tile === move.color).length : 0;
      score += tileCount;
    } else {
      const centerTiles = gameState.centerArea.tiles || gameState.centerArea;
      const tileCount = Array.isArray(centerTiles) ? centerTiles.filter(tile => tile === move.color).length : 0;
      score += tileCount;

      // 从中央区域选择时，如果能获得首个玩家标记，加分
      if (gameState.firstPlayerMarker) {
        score += 3;
      }
    }

    return score;
  }

  // 获取剩余时间
  getRemainingTime(roomId) {
    const timerInfo = this.turnTimers.get(roomId);
    if (!timerInfo) {
      console.log(`获取剩余时间失败，房间 ${roomId} 没有计时器`);
      return 0;
    }

    const elapsed = Date.now() - timerInfo.startTime;
    const remaining = Math.max(0, this.turnTimeLimit - elapsed);

    // 如果剩余时间很少，输出调试信息
    if (remaining < 10000) {
      console.log(`房间 ${roomId} 剩余时间: ${remaining}ms, 已过时间: ${elapsed}ms`);
    }

    return remaining;
  }
}

module.exports = AzulGameEngine;
