#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
阿里云短信服务验证码群发脚本
"""

import json
import time
import schedule
import pandas as pd
from datetime import datetime
from alibabacloud_dysmsapi20170525.client import Client as Dysmsapi20170525Client
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_dysmsapi20170525 import models as dysmsapi_20170525_models
from alibabacloud_tea_util import models as util_models
from alibabacloud_tea_util.client import Client as UtilClient

class AliyunSMSSender:
    def __init__(self, access_key_id, access_key_secret, sign_name, template_code):
        """
        初始化阿里云短信客户端
        
        Args:
            access_key_id: 阿里云AccessKey ID
            access_key_secret: 阿里云AccessKey Secret
            sign_name: 短信签名
            template_code: 短信模板代码
        """
        self.access_key_id = access_key_id
        self.access_key_secret = access_key_secret
        self.sign_name = sign_name
        self.template_code = template_code
        self.client = self._create_client()
        
    def _create_client(self):
        """创建阿里云短信客户端"""
        config = open_api_models.Config(
            access_key_id=self.access_key_id,
            access_key_secret=self.access_key_secret
        )
        # 访问的域名
        config.endpoint = 'dysmsapi.aliyuncs.com'
        return Dysmsapi20170525Client(config)
    
    def send_sms(self, phone_number, verification_code):
        """
        发送单条短信
        
        Args:
            phone_number: 手机号码
            verification_code: 验证码
            
        Returns:
            bool: 发送是否成功
        """
        try:
            send_sms_request = dysmsapi_20170525_models.SendSmsRequest(
                phone_numbers=phone_number,
                sign_name=self.sign_name,
                template_code=self.template_code,
                template_param=json.dumps({"code": verification_code})
            )
            
            runtime = util_models.RuntimeOptions()
            response = self.client.send_sms_with_options(send_sms_request, runtime)
            
            if response.body.code == 'OK':
                print(f"✅ 短信发送成功: {phone_number} - 验证码: {verification_code}")
                return True
            else:
                print(f"❌ 短信发送失败: {phone_number} - 错误: {response.body.message}")
                return False
                
        except Exception as e:
            print(f"❌ 发送短信异常: {phone_number} - 错误: {str(e)}")
            return False
    
    def send_batch_sms(self, excel_file_path):
        """
        批量发送短信
        
        Args:
            excel_file_path: Excel文件路径
        """
        try:
            # 读取Excel文件
            df = pd.read_excel(excel_file_path)
            
            # 打印列名以便调试
            print(f"Excel文件列名: {list(df.columns)}")
            
            # 假设Excel文件有两列：手机号和验证码
            # 根据实际情况调整列名
            phone_column = None
            code_column = None
            
            # 尝试匹配可能的列名
            for col in df.columns:
                col_lower = str(col).lower()
                if any(keyword in col_lower for keyword in ['phone', '手机', '电话', 'mobile', 'tel']):
                    phone_column = col
                elif any(keyword in col_lower for keyword in ['code', '验证码', '代码', 'verification']):
                    code_column = col
            
            if phone_column is None or code_column is None:
                print("❌ 无法识别Excel文件中的手机号和验证码列，请检查文件格式")
                print(f"可用列: {list(df.columns)}")
                return
            
            print(f"📱 手机号列: {phone_column}")
            print(f"🔢 验证码列: {code_column}")
            print(f"📊 总共需要发送 {len(df)} 条短信")
            
            success_count = 0
            fail_count = 0
            
            for index, row in df.iterrows():
                phone = str(row[phone_column]).strip()
                code = str(row[code_column]).strip()
                
                # 验证手机号格式（简单验证）
                if not phone.isdigit() or len(phone) != 11:
                    print(f"⚠️  跳过无效手机号: {phone}")
                    fail_count += 1
                    continue
                
                # 发送短信
                if self.send_sms(phone, code):
                    success_count += 1
                else:
                    fail_count += 1
                
                # 避免发送过快，每条短信间隔1秒
                time.sleep(1)
            
            print(f"\n📈 发送统计:")
            print(f"✅ 成功: {success_count} 条")
            print(f"❌ 失败: {fail_count} 条")
            print(f"📅 发送时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            
        except Exception as e:
            print(f"❌ 批量发送短信异常: {str(e)}")

def main():
    """主函数"""
    # 配置信息
    ACCESS_KEY_ID = "LTAI5tGfGC4ULLLmg3nTqAWL"
    ACCESS_KEY_SECRET = "******************************"
    SIGN_NAME = "杭州金柝信息咨询"
    TEMPLATE_CODE = "SMS_318730293"
    EXCEL_FILE_PATH = r"C:\Users\<USER>\Downloads\1870931502869162_SMS_318730293_saasUploadTemplate.xlsx"
    
    # 创建短信发送器
    sms_sender = AliyunSMSSender(
        access_key_id=ACCESS_KEY_ID,
        access_key_secret=ACCESS_KEY_SECRET,
        sign_name=SIGN_NAME,
        template_code=TEMPLATE_CODE
    )
    
    def send_scheduled_sms():
        """定时发送短信的函数"""
        print(f"\n🚀 开始执行定时短信发送任务 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        sms_sender.send_batch_sms(EXCEL_FILE_PATH)
        print("=" * 50)
    
    # 立即执行一次
    print("🎯 立即执行一次短信发送...")
    send_scheduled_sms()
    
    # 设置定时任务：每30分钟执行一次
    schedule.every(30).minutes.do(send_scheduled_sms)
    
    print(f"\n⏰ 定时任务已设置，每30分钟发送一次短信")
    print("按 Ctrl+C 停止程序")
    
    # 运行定时任务
    try:
        while True:
            schedule.run_pending()
            time.sleep(60)  # 每分钟检查一次
    except KeyboardInterrupt:
        print("\n👋 程序已停止")

if __name__ == "__main__":
    main()
