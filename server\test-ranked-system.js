// 花砖物语排位赛系统测试
const DatabaseManager = require('./database');
const RankingSystem = require('./rankingSystem');
const MatchmakingSystem = require('./matchmakingSystem');

async function testRankedSystem() {
  console.log('🧪 开始测试排位赛系统...\n');

  try {
    // 1. 测试数据库管理器
    console.log('1. 测试数据库管理器');
    const db = new DatabaseManager();
    await db.init();

    // 创建测试用户
    const testUsers = [
      { openid: 'user1', nickName: '玩家1', avatarUrl: '' },
      { openid: 'user2', nickName: '玩家2', avatarUrl: '' },
      { openid: 'user3', nickName: '玩家3', avatarUrl: '' },
      { openid: 'user4', nickName: '玩家4', avatarUrl: '' }
    ];

    for (const userInfo of testUsers) {
      const user = await db.getOrCreateUser(userInfo.openid, userInfo);
      console.log(`✅ 创建用户: ${user.nickname} (${user.rating}分)`);
    }

    // 2. 测试积分系统
    console.log('\n2. 测试积分系统');
    const ranking = new RankingSystem();

    // 测试段位计算
    const testRatings = [800, 1200, 1400, 1600, 1800, 2200];
    testRatings.forEach(rating => {
      const tier = ranking.calculateTier(rating);
      console.log(`✅ ${rating}分 -> ${tier.tierName} ${tier.division} (${tier.progress}%)`);
    });

    // 测试积分变化计算
    const players = [
      { id: 'user1', rating: 1200 },
      { id: 'user2', rating: 1250 },
      { id: 'user3', rating: 1180 },
      { id: 'user4', rating: 1220 }
    ];

    const finalScores = [85, 72, 68, 45]; // 模拟游戏分数
    const ratingChanges = ranking.calculateRatingChanges(players, finalScores);

    console.log('\n积分变化计算结果:');
    ratingChanges.forEach((change, index) => {
      console.log(`✅ ${players[index].id}: ${change.oldRating} -> ${change.newRating} (${change.ratingChange > 0 ? '+' : ''}${change.ratingChange})`);
    });

    // 3. 测试匹配系统
    console.log('\n3. 测试匹配系统');
    const matchmaking = new MatchmakingSystem(ranking, db);

    // 测试匹配质量计算
    const matchQuality = ranking.calculateMatchQuality(players);
    console.log(`✅ 匹配质量: ${matchQuality}%`);

    // 测试匹配范围
    const matchRange = ranking.getMatchingRange(1200, 0);
    console.log(`✅ 1200分匹配范围: ${matchRange.min} - ${matchRange.max}`);

    const matchRangeWait = ranking.getMatchingRange(1200, 60);
    console.log(`✅ 1200分等待60秒后匹配范围: ${matchRangeWait.min} - ${matchRangeWait.max}`);

    // 4. 测试数据库操作
    console.log('\n4. 测试数据库操作');

    // 记录模拟比赛
    const matchId = 'test_match_' + Date.now();
    await db.recordMatch({
      matchId: matchId,
      players: players,
      winnerId: 'user1',
      duration: 1200,
      finalScores: finalScores,
      ratingChanges: ratingChanges
    });
    console.log(`✅ 记录比赛: ${matchId}`);

    // 更新玩家积分和统计
    for (let i = 0; i < players.length; i++) {
      const player = players[i];
      const change = ratingChanges[i];
      
      await db.updateUserRating(
        player.id,
        change.newRating,
        change.newTier.tier,
        change.newTier.division
      );

      await db.updateUserStats(player.id, change.placement === 1, change.ratingChange);

      await db.recordRatingHistory({
        openid: player.id,
        matchId: matchId,
        oldRating: change.oldRating,
        newRating: change.newRating,
        oldTier: `${change.oldTier.tier}_${change.oldTier.division}`,
        newTier: `${change.newTier.tier}_${change.newTier.division}`,
        placement: change.placement,
        finalScore: change.finalScore
      });

      console.log(`✅ 更新玩家 ${player.id} 数据`);
    }

    // 5. 测试排行榜
    console.log('\n5. 测试排行榜');
    const leaderboard = await db.getLeaderboard(10);
    console.log('排行榜:');
    leaderboard.forEach((player, index) => {
      const tier = ranking.calculateTier(player.rating);
      console.log(`${index + 1}. ${player.nickname} - ${player.rating}分 (${tier.tierName} ${tier.division})`);
    });

    // 6. 测试用户排名
    console.log('\n6. 测试用户排名');
    for (const user of testUsers) {
      const rank = await db.getUserRank(user.openid);
      console.log(`✅ ${user.nickName} 排名: #${rank}`);
    }

    // 7. 测试历史记录
    console.log('\n7. 测试历史记录');
    const history = await db.getUserHistory('user1', 5);
    console.log(`用户1历史记录 (${history.length}条):`);
    history.forEach(record => {
      console.log(`- ${record.old_rating} -> ${record.new_rating} (${record.rating_change > 0 ? '+' : ''}${record.rating_change}) 第${record.placement}名`);
    });

    // 8. 保存数据
    console.log('\n8. 保存数据');
    await db.saveData();
    console.log('✅ 数据保存完成');

    await db.close();
    console.log('\n🎉 排位赛系统测试完成！所有功能正常工作。');

  } catch (error) {
    console.error('❌ 测试失败:', error);
    process.exit(1);
  }
}

// 运行测试
if (require.main === module) {
  testRankedSystem().then(() => {
    console.log('\n✅ 测试程序结束');
    process.exit(0);
  }).catch(error => {
    console.error('❌ 测试程序异常:', error);
    process.exit(1);
  });
}

module.exports = { testRankedSystem };
