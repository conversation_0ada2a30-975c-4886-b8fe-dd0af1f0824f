// 花砖物语排位赛UI管理器
class RankedUI {
  constructor() {
    this.multiplayerManager = null;
    this.currentScreen = 'main'; // main, queue, match
    this.userRanking = null;
    this.queueStatus = null;
    this.matchData = null;
    this.callbacks = {};
  }

  // 初始化
  init(multiplayerManager) {
    this.multiplayerManager = multiplayerManager;
    this.setupEventListeners();
    console.log('排位赛UI初始化完成');
  }

  // 设置事件监听器
  setupEventListeners() {
    // 监听排位赛相关事件
    this.multiplayerManager.on('userRanking', (data) => {
      this.userRanking = data;
      this.updateRankingDisplay();
    });

    this.multiplayerManager.on('queueJoined', (data) => {
      this.queueStatus = data;
      this.showQueueScreen();
    });

    this.multiplayerManager.on('queueUpdate', (data) => {
      this.queueStatus = { ...this.queueStatus, ...data };
      this.updateQueueDisplay();
    });

    this.multiplayerManager.on('queueLeft', () => {
      this.queueStatus = null;
      this.showMainScreen();
    });

    this.multiplayerManager.on('matchFound', (data) => {
      this.matchData = data;
      this.showMatchFoundScreen();
    });

    this.multiplayerManager.on('rankedGameStarting', (data) => {
      this.hideUI();
      this.emit('gameStarting', data);
    });

    // 注释掉自动显示排位赛结果界面，让游戏界面自己处理结束逻辑
    // this.multiplayerManager.on('rankedGameCompleted', (data) => {
    //   this.showGameResultScreen(data);
    // });
  }

  // 显示排位赛主界面
  showMainScreen() {
    this.currentScreen = 'main';
    this.hideAllScreens();

    const canvas = this.getCanvas();
    const ctx = canvas.getContext('2d');
    const { width, height } = this.getLogicalSize();

    // 清空画布
    ctx.fillStyle = '#1a1a2e';
    ctx.fillRect(0, 0, width, height);

    // 标题
    ctx.fillStyle = '#ffffff';
    ctx.font = 'bold 32px Arial';
    ctx.textAlign = 'center';
    ctx.fillText('排位赛', width / 2, 80);

    // 用户排位信息
    if (this.userRanking) {
      this.drawUserRankingCard(ctx, width, height);
    }

    // 按钮
    this.drawMainButtons(ctx, width, height);

    // 绑定点击事件
    this.bindMainScreenEvents();
  }

  // 绘制用户排位卡片
  drawUserRankingCard(ctx, width, height) {
    const cardX = width / 2 - 200;
    const cardY = 120;
    const cardWidth = 400;
    const cardHeight = 150;

    // 卡片背景
    ctx.fillStyle = '#2d2d44';
    ctx.fillRect(cardX, cardY, cardWidth, cardHeight);
    ctx.strokeStyle = '#4a4a6a';
    ctx.lineWidth = 2;
    ctx.strokeRect(cardX, cardY, cardWidth, cardHeight);

    // 段位图标和名称
    ctx.fillStyle = this.userRanking.tier.color;
    ctx.font = 'bold 24px Arial';
    ctx.textAlign = 'left';
    ctx.fillText(
      `${this.userRanking.tier.icon} ${this.userRanking.tier.name}`,
      cardX + 20,
      cardY + 40
    );

    // 积分
    ctx.fillStyle = '#ffffff';
    ctx.font = '18px Arial';
    ctx.fillText(`积分: ${this.userRanking.rating}`, cardX + 20, cardY + 70);

    // 排名
    if (this.userRanking.rank) {
      ctx.fillText(`排名: #${this.userRanking.rank}`, cardX + 200, cardY + 70);
    }

    // 胜率
    const winRate = (this.userRanking.winRate * 100).toFixed(1);
    ctx.fillText(`胜率: ${winRate}%`, cardX + 20, cardY + 100);

    // 连胜
    if (this.userRanking.currentStreak > 0) {
      ctx.fillStyle = '#4CAF50';
      ctx.fillText(`连胜: ${this.userRanking.currentStreak}`, cardX + 200, cardY + 100);
    }

    // 段位进度条
    if (this.userRanking.tier.progress !== undefined) {
      this.drawProgressBar(ctx, cardX + 20, cardY + 120, cardWidth - 40, 15, this.userRanking.tier.progress);
    }
  }

  // 绘制进度条
  drawProgressBar(ctx, x, y, width, height, progress) {
    // 背景
    ctx.fillStyle = '#1a1a2e';
    ctx.fillRect(x, y, width, height);

    // 进度
    ctx.fillStyle = '#4CAF50';
    ctx.fillRect(x, y, (width * progress) / 100, height);

    // 边框
    ctx.strokeStyle = '#4a4a6a';
    ctx.lineWidth = 1;
    ctx.strokeRect(x, y, width, height);

    // 进度文字
    ctx.fillStyle = '#ffffff';
    ctx.font = '12px Arial';
    ctx.textAlign = 'center';
    ctx.fillText(`${progress}%`, x + width / 2, y + height - 2);
  }

  // 绘制主界面按钮
  drawMainButtons(ctx, width, height) {
    const buttonWidth = 200;
    const buttonHeight = 50;
    const buttonY = height - 200;

    // 开始匹配按钮
    const startButton = {
      x: width / 2 - buttonWidth - 10,
      y: buttonY,
      width: buttonWidth,
      height: buttonHeight,
      text: '开始匹配',
      color: '#4CAF50'
    };

    // 排行榜按钮
    const leaderboardButton = {
      x: width / 2 + 10,
      y: buttonY,
      width: buttonWidth,
      height: buttonHeight,
      text: '排行榜',
      color: '#2196F3'
    };

    this.drawButton(ctx, startButton);
    this.drawButton(ctx, leaderboardButton);

    // 返回按钮
    const backButton = {
      x: 50,
      y: height - 80,
      width: 100,
      height: 40,
      text: '返回',
      color: '#757575'
    };

    this.drawButton(ctx, backButton);

    // 存储按钮位置用于点击检测
    this.buttons = {
      startMatch: startButton,
      leaderboard: leaderboardButton,
      back: backButton
    };
  }

  // 绘制按钮
  drawButton(ctx, button) {
    // 按钮背景
    ctx.fillStyle = button.color;
    ctx.fillRect(button.x, button.y, button.width, button.height);

    // 按钮边框
    ctx.strokeStyle = '#ffffff';
    ctx.lineWidth = 2;
    ctx.strokeRect(button.x, button.y, button.width, button.height);

    // 按钮文字
    ctx.fillStyle = '#ffffff';
    ctx.font = 'bold 18px Arial';
    ctx.textAlign = 'center';
    ctx.fillText(
      button.text,
      button.x + button.width / 2,
      button.y + button.height / 2 + 6
    );
  }

  // 显示匹配队列界面
  showQueueScreen() {
    this.currentScreen = 'queue';
    this.hideAllScreens();

    const canvas = this.getCanvas();
    const ctx = canvas.getContext('2d');
    const { width, height } = this.getLogicalSize();

    // 清空画布
    ctx.fillStyle = '#1a1a2e';
    ctx.fillRect(0, 0, width, height);

    this.updateQueueDisplay();
    this.bindQueueScreenEvents();
  }

  // 更新队列显示
  updateQueueDisplay() {
    if (this.currentScreen !== 'queue' || !this.queueStatus) return;

    const canvas = this.getCanvas();
    const ctx = canvas.getContext('2d');
    const { width, height } = this.getLogicalSize();

    // 清空画布
    ctx.fillStyle = '#1a1a2e';
    ctx.fillRect(0, 0, width, height);

    // 标题
    ctx.fillStyle = '#ffffff';
    ctx.font = 'bold 28px Arial';
    ctx.textAlign = 'center';
    ctx.fillText('正在匹配...', width / 2, 100);

    // 匹配状态
    ctx.font = '20px Arial';
    ctx.fillText(`队列位置: ${this.queueStatus.position}`, width / 2, 180);
    
    const waitTime = Math.floor(this.queueStatus.waitTime || 0);
    ctx.fillText(`等待时间: ${waitTime}秒`, width / 2, 220);
    
    const estimatedTime = this.queueStatus.estimatedWaitTime || 0;
    ctx.fillText(`预计等待: ${estimatedTime}秒`, width / 2, 260);

    // 加载动画
    this.drawLoadingAnimation(ctx, width / 2, 320);

    // 取消按钮
    const cancelButton = {
      x: width / 2 - 100,
      y: height - 150,
      width: 200,
      height: 50,
      text: '取消匹配',
      color: '#f44336'
    };

    this.drawButton(ctx, cancelButton);
    this.buttons = { cancel: cancelButton };
  }

  // 绘制加载动画
  drawLoadingAnimation(ctx, centerX, centerY) {
    const time = Date.now() / 100;
    const radius = 30;
    
    for (let i = 0; i < 8; i++) {
      const angle = (i * Math.PI * 2) / 8 + time * 0.1;
      const x = centerX + Math.cos(angle) * radius;
      const y = centerY + Math.sin(angle) * radius;
      
      const alpha = (Math.sin(time * 0.1 + i * 0.5) + 1) / 2;
      ctx.fillStyle = `rgba(76, 175, 80, ${alpha})`;
      ctx.beginPath();
      ctx.arc(x, y, 5, 0, Math.PI * 2);
      ctx.fill();
    }
  }

  // 显示匹配成功界面
  showMatchFoundScreen() {
    this.currentScreen = 'match';
    this.hideAllScreens();

    const canvas = this.getCanvas();
    const ctx = canvas.getContext('2d');
    const { width, height } = this.getLogicalSize();

    // 清空画布
    ctx.fillStyle = '#1a1a2e';
    ctx.fillRect(0, 0, width, height);

    // 标题
    ctx.fillStyle = '#4CAF50';
    ctx.font = 'bold 32px Arial';
    ctx.textAlign = 'center';
    ctx.fillText('匹配成功！', width / 2, 80);

    // 匹配质量
    if (this.matchData.quality) {
      ctx.fillStyle = '#ffffff';
      ctx.font = '18px Arial';
      ctx.fillText(`匹配质量: ${this.matchData.quality}%`, width / 2, 120);
    }

    // 玩家列表
    if (this.matchData.players) {
      this.drawPlayerList(ctx, width, height);
    }

    // 倒计时
    this.startMatchCountdown();
  }

  // 绘制玩家列表
  drawPlayerList(ctx, width, height) {
    const players = this.matchData.players;
    const startY = 160;
    const playerHeight = 60;

    ctx.fillStyle = '#ffffff';
    ctx.font = '16px Arial';
    ctx.textAlign = 'left';

    players.forEach((player, index) => {
      const y = startY + index * playerHeight;
      
      // 玩家背景
      ctx.fillStyle = '#2d2d44';
      ctx.fillRect(width / 2 - 200, y - 20, 400, 50);
      
      // 玩家信息
      ctx.fillStyle = '#ffffff';
      ctx.fillText(player.nickName, width / 2 - 180, y);
      ctx.fillText(`${player.rating}分`, width / 2 + 100, y);
      
      // 段位
      ctx.fillStyle = player.tier ? '#FFD700' : '#C0C0C0';
      ctx.fillText(`${player.tier || 'Bronze'} ${player.division || 5}`, width / 2 - 50, y);
    });
  }

  // 开始匹配倒计时
  startMatchCountdown() {
    let countdown = 10;
    
    const updateCountdown = () => {
      if (this.currentScreen !== 'match') return;
      
      const canvas = this.getCanvas();
      const ctx = canvas.getContext('2d');
      const { width, height } = this.getLogicalSize();
      
      // 清除倒计时区域
      ctx.fillStyle = '#1a1a2e';
      ctx.fillRect(width / 2 - 100, height - 150, 200, 100);
      
      // 绘制倒计时
      ctx.fillStyle = countdown <= 3 ? '#f44336' : '#4CAF50';
      ctx.font = 'bold 48px Arial';
      ctx.textAlign = 'center';
      ctx.fillText(countdown.toString(), width / 2, height - 80);
      
      countdown--;
      
      if (countdown >= 0) {
        setTimeout(updateCountdown, 1000);
      }
    };
    
    updateCountdown();
  }

  // 获取Canvas
  getCanvas() {
    if (typeof wx !== 'undefined' && wx.createCanvas) {
      return wx.createCanvas();
    } else {
      return document.getElementById('gameCanvas');
    }
  }

  // 获取逻辑尺寸
  getLogicalSize() {
    if (typeof wx !== 'undefined') {
      const info = wx.getSystemInfoSync();
      return { width: info.windowWidth, height: info.windowHeight };
    } else {
      return { width: 800, height: 600 };
    }
  }

  // 隐藏所有界面
  hideAllScreens() {
    // 清除所有定时器和事件监听器
    this.cleanup();
  }

  // 隐藏UI
  hideUI() {
    this.currentScreen = 'hidden';
    this.cleanup();
  }

  // 清理资源
  cleanup() {
    // 清理事件监听器等
    this.buttons = {};
  }

  // 事件发射器
  emit(event, data) {
    if (this.callbacks[event]) {
      this.callbacks[event].forEach(callback => callback(data));
    }
  }

  // 事件监听器
  on(event, callback) {
    if (!this.callbacks[event]) {
      this.callbacks[event] = [];
    }
    this.callbacks[event].push(callback);
  }

  // 绑定主界面事件
  bindMainScreenEvents() {
    const canvas = this.getCanvas();

    const handleClick = (event) => {
      const rect = canvas.getBoundingClientRect ? canvas.getBoundingClientRect() : { left: 0, top: 0 };
      const x = (event.clientX || event.touches[0].clientX) - rect.left;
      const y = (event.clientY || event.touches[0].clientY) - rect.top;

      // 检查按钮点击
      if (this.isPointInButton(x, y, this.buttons.startMatch)) {
        this.startRankedMatch();
      } else if (this.isPointInButton(x, y, this.buttons.leaderboard)) {
        this.showLeaderboard();
      } else if (this.isPointInButton(x, y, this.buttons.back)) {
        this.emit('back');
      }
    };

    // 绑定点击事件
    if (typeof wx !== 'undefined') {
      wx.onTouchStart(handleClick);
    } else {
      canvas.addEventListener('click', handleClick);
    }
  }

  // 绑定队列界面事件
  bindQueueScreenEvents() {
    const canvas = this.getCanvas();

    const handleClick = (event) => {
      const rect = canvas.getBoundingClientRect ? canvas.getBoundingClientRect() : { left: 0, top: 0 };
      const x = (event.clientX || event.touches[0].clientX) - rect.left;
      const y = (event.clientY || event.touches[0].clientY) - rect.top;

      if (this.isPointInButton(x, y, this.buttons.cancel)) {
        this.cancelRankedMatch();
      }
    };

    if (typeof wx !== 'undefined') {
      wx.onTouchStart(handleClick);
    } else {
      canvas.addEventListener('click', handleClick);
    }
  }

  // 检查点是否在按钮内
  isPointInButton(x, y, button) {
    return x >= button.x && x <= button.x + button.width &&
           y >= button.y && y <= button.y + button.height;
  }

  // 开始排位匹配
  async startRankedMatch() {
    try {
      // 防重复点击保护
      if (this.isStartingMatch) {
        console.log('正在开始匹配，忽略重复点击');
        return;
      }

      this.isStartingMatch = true;

      if (!this.multiplayerManager) {
        throw new Error('多人游戏管理器未初始化');
      }

      // 先获取用户排位信息
      await this.loadUserRanking();

      // 加入排位队列
      await this.multiplayerManager.joinRankedQueue();

    } catch (error) {
      console.error('开始排位匹配失败:', error);
      this.showError('开始匹配失败: ' + error.message);
    } finally {
      // 无论成功还是失败，都要清除保护标志
      this.isStartingMatch = false;
    }
  }

  // 取消排位匹配
  async cancelRankedMatch() {
    try {
      if (!this.multiplayerManager) {
        throw new Error('多人游戏管理器未初始化');
      }

      await this.multiplayerManager.leaveRankedQueue();

    } catch (error) {
      console.error('取消排位匹配失败:', error);
      this.showError('取消匹配失败: ' + error.message);
    }
  }

  // 加载用户排位信息
  async loadUserRanking() {
    try {
      if (!this.multiplayerManager) {
        throw new Error('多人游戏管理器未初始化');
      }

      await this.multiplayerManager.getUserRanking();

    } catch (error) {
      console.error('加载用户排位信息失败:', error);
      // 不阻塞匹配流程，使用默认值
      this.userRanking = {
        rating: 1200,
        tier: { name: '青铜 5', icon: '🥉', color: '#CD7F32', progress: 0 },
        rank: null,
        totalGames: 0,
        wins: 0,
        losses: 0,
        winRate: 0,
        currentStreak: 0,
        highestRating: 1200
      };
    }
  }

  // 显示排行榜
  async showLeaderboard() {
    try {
      if (!this.multiplayerManager) {
        throw new Error('多人游戏管理器未初始化');
      }

      const leaderboard = await this.multiplayerManager.getLeaderboard();
      this.showLeaderboardScreen(leaderboard);

    } catch (error) {
      console.error('获取排行榜失败:', error);
      this.showError('获取排行榜失败: ' + error.message);
    }
  }

  // 显示排行榜界面
  showLeaderboardScreen(leaderboard) {
    this.currentScreen = 'leaderboard';
    this.hideAllScreens();

    const canvas = this.getCanvas();
    const ctx = canvas.getContext('2d');
    const { width, height } = this.getLogicalSize();

    // 清空画布
    ctx.fillStyle = '#1a1a2e';
    ctx.fillRect(0, 0, width, height);

    // 标题
    ctx.fillStyle = '#ffffff';
    ctx.font = 'bold 28px Arial';
    ctx.textAlign = 'center';
    ctx.fillText('排行榜', width / 2, 60);

    // 绘制排行榜列表
    this.drawLeaderboardList(ctx, width, height, leaderboard.leaderboard);

    // 返回按钮
    const backButton = {
      x: 50,
      y: height - 80,
      width: 100,
      height: 40,
      text: '返回',
      color: '#757575'
    };

    this.drawButton(ctx, backButton);
    this.buttons = { back: backButton };

    this.bindLeaderboardEvents();
  }

  // 绘制排行榜列表
  drawLeaderboardList(ctx, width, height, leaderboard) {
    const startY = 100;
    const itemHeight = 50;
    const maxItems = Math.floor((height - 200) / itemHeight);

    ctx.font = '16px Arial';
    ctx.textAlign = 'left';

    leaderboard.slice(0, maxItems).forEach((player, index) => {
      const y = startY + index * itemHeight;

      // 背景
      ctx.fillStyle = index % 2 === 0 ? '#2d2d44' : '#1a1a2e';
      ctx.fillRect(50, y - 15, width - 100, itemHeight - 5);

      // 排名
      ctx.fillStyle = index < 3 ? '#FFD700' : '#ffffff';
      ctx.font = 'bold 18px Arial';
      ctx.fillText(`#${player.rank}`, 70, y + 10);

      // 玩家名称
      ctx.fillStyle = '#ffffff';
      ctx.font = '16px Arial';
      ctx.fillText(player.nickname, 130, y + 10);

      // 段位
      if (player.tier) {
        ctx.fillStyle = player.tier.color;
        ctx.fillText(`${player.tier.icon} ${player.tier.name}`, 300, y + 10);
      }

      // 积分
      ctx.fillStyle = '#4CAF50';
      ctx.fillText(`${player.rating}分`, width - 200, y + 10);

      // 胜率
      const winRate = (player.win_rate * 100).toFixed(1);
      ctx.fillStyle = '#2196F3';
      ctx.fillText(`${winRate}%`, width - 100, y + 10);
    });
  }

  // 绑定排行榜事件
  bindLeaderboardEvents() {
    const canvas = this.getCanvas();

    const handleClick = (event) => {
      const rect = canvas.getBoundingClientRect ? canvas.getBoundingClientRect() : { left: 0, top: 0 };
      const x = (event.clientX || event.touches[0].clientX) - rect.left;
      const y = (event.clientY || event.touches[0].clientY) - rect.top;

      if (this.isPointInButton(x, y, this.buttons.back)) {
        this.showMainScreen();
      }
    };

    if (typeof wx !== 'undefined') {
      wx.onTouchStart(handleClick);
    } else {
      canvas.addEventListener('click', handleClick);
    }
  }

  // 显示游戏结果界面
  showGameResultScreen(result) {
    this.currentScreen = 'result';
    this.hideAllScreens();

    const canvas = this.getCanvas();
    const ctx = canvas.getContext('2d');
    const { width, height } = this.getLogicalSize();

    // 清空画布
    ctx.fillStyle = '#1a1a2e';
    ctx.fillRect(0, 0, width, height);

    // 标题
    const isWin = result.ratingChange.placement === 1;
    ctx.fillStyle = isWin ? '#4CAF50' : '#f44336';
    ctx.font = 'bold 32px Arial';
    ctx.textAlign = 'center';
    ctx.fillText(isWin ? '胜利！' : '失败', width / 2, 80);

    // 排名
    ctx.fillStyle = '#ffffff';
    ctx.font = '24px Arial';
    ctx.fillText(`第 ${result.ratingChange.placement} 名`, width / 2, 130);

    // 积分变化
    const ratingChange = result.ratingChange.ratingChange;
    ctx.fillStyle = ratingChange > 0 ? '#4CAF50' : '#f44336';
    ctx.font = 'bold 28px Arial';
    ctx.fillText(
      `${ratingChange > 0 ? '+' : ''}${ratingChange}`,
      width / 2,
      180
    );

    // 新积分和段位
    ctx.fillStyle = '#ffffff';
    ctx.font = '20px Arial';
    ctx.fillText(`新积分: ${result.newRating}`, width / 2, 220);

    if (result.newTier) {
      ctx.fillStyle = result.newTier.color;
      ctx.fillText(
        `${result.newTier.icon} ${result.newTier.name}`,
        width / 2,
        260
      );
    }

    // 段位变化提示
    if (result.ratingChange.tierChanged) {
      ctx.fillStyle = '#FFD700';
      ctx.font = 'bold 18px Arial';
      ctx.fillText('段位变化！', width / 2, 300);
    }

    // 继续按钮
    const continueButton = {
      x: width / 2 - 100,
      y: height - 150,
      width: 200,
      height: 50,
      text: '继续',
      color: '#4CAF50'
    };

    this.drawButton(ctx, continueButton);
    this.buttons = { continue: continueButton };

    this.bindResultEvents();
  }

  // 绑定结果界面事件
  bindResultEvents() {
    const canvas = this.getCanvas();

    const handleClick = (event) => {
      const rect = canvas.getBoundingClientRect ? canvas.getBoundingClientRect() : { left: 0, top: 0 };
      const x = (event.clientX || event.touches[0].clientX) - rect.left;
      const y = (event.clientY || event.touches[0].clientY) - rect.top;

      if (this.isPointInButton(x, y, this.buttons.continue)) {
        // 排位赛结束后，主动离开排位赛房间
        if (this.multiplayerManager && this.multiplayerManager.roomId) {
          console.log('排位赛结束，离开房间:', this.multiplayerManager.roomId);
          this.multiplayerManager.leaveRoom();
        }

        this.showMainScreen();
        this.loadUserRanking(); // 刷新用户数据
      }
    };

    if (typeof wx !== 'undefined') {
      wx.onTouchStart(handleClick);
    } else {
      canvas.addEventListener('click', handleClick);
    }
  }

  // 显示错误信息
  showError(message) {
    console.error('排位赛UI错误:', message);

    // 简单的错误提示
    if (typeof wx !== 'undefined') {
      wx.showToast({
        title: message,
        icon: 'none',
        duration: 3000
      });
    } else {
      alert(message);
    }
  }

  // 更新排位显示
  updateRankingDisplay() {
    if (this.currentScreen === 'main') {
      this.showMainScreen();
    }
  }
}
