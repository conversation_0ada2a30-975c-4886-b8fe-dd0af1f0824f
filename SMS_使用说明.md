# 阿里云短信服务验证码群发脚本使用说明

## 功能特点

- ✅ 支持从Excel文件读取手机号和验证码
- ✅ 自动识别Excel文件中的手机号和验证码列
- ✅ 每30分钟自动发送一次短信
- ✅ 详细的发送日志和统计信息
- ✅ 手机号格式验证
- ✅ 发送频率控制（每条短信间隔1秒）
- ✅ 异常处理和错误提示

## 安装步骤

1. **安装Python依赖**：
   ```bash
   pip install -r requirements.txt
   ```

2. **确认Excel文件路径**：
   脚本中配置的Excel文件路径为：
   `C:\Users\<USER>\Downloads\1870931502869162_SMS_318730293_saasUploadTemplate.xlsx`

## 配置信息

脚本已配置以下信息：
- **签名**: 杭州金柝信息咨询
- **模板代码**: SMS_318730293
- **模板内容**: 您的验证码为：${code}，请勿泄露于他人！

## 使用方法

1. **运行脚本**：
   ```bash
   python sms_sender.py
   ```

2. **程序执行流程**：
   - 立即执行一次短信发送
   - 设置定时任务，每30分钟发送一次
   - 按 `Ctrl+C` 可停止程序

## Excel文件格式要求

Excel文件应包含手机号和验证码两列，脚本会自动识别包含以下关键词的列：

**手机号列**：phone、手机、电话、mobile、tel
**验证码列**：code、验证码、代码、verification

## 输出示例

```
🎯 立即执行一次短信发送...
Excel文件列名: ['手机号', '验证码']
📱 手机号列: 手机号
🔢 验证码列: 验证码
📊 总共需要发送 10 条短信
✅ 短信发送成功: 13800138000 - 验证码: 123456
✅ 短信发送成功: 13900139000 - 验证码: 789012

📈 发送统计:
✅ 成功: 8 条
❌ 失败: 2 条
📅 发送时间: 2024-01-15 10:30:15
```

## 注意事项

1. **安全提醒**: 请妥善保管AccessKey信息
2. **发送频率**: 每条短信间隔1秒，避免发送过快
3. **手机号验证**: 只发送11位数字的有效手机号
4. **Excel格式**: 确保文件格式正确，列名包含关键词

## 故障排除

- **Excel文件读取失败**: 检查文件路径和格式
- **列名识别失败**: 确保列名包含相关关键词
- **短信发送失败**: 检查账户余额和配置信息
