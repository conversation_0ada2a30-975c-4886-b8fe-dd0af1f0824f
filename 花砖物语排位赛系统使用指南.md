# 花砖物语排位赛系统使用指南

## 🎯 系统概述

花砖物语排位赛系统是一个完整的竞技匹配系统，包含积分计算、段位系统、自动匹配、排行榜等功能。

### ✨ 主要特性

- **ELO积分系统**: 基于经典ELO算法的动态积分计算
- **段位系统**: 青铜、白银、黄金、铂金、钻石、大师六个段位
- **智能匹配**: 根据积分和等待时间进行最优匹配
- **实时排行榜**: 全服排行榜和个人历史记录
- **数据持久化**: JSON文件存储，支持数据备份和恢复

## 🏗️ 系统架构

### 服务端组件

1. **DatabaseManager** (`server/database.js`)
   - 用户数据管理
   - 比赛记录存储
   - 积分历史追踪

2. **RankingSystem** (`server/rankingSystem.js`)
   - ELO积分计算
   - 段位系统管理
   - 匹配质量评估

3. **MatchmakingSystem** (`server/matchmakingSystem.js`)
   - 自动匹配队列
   - 匹配算法优化
   - 游戏创建管理

4. **RankedGameEngine** (`server/rankedGameEngine.js`)
   - 排位赛游戏逻辑
   - 严格计时控制
   - 断线重连处理

### 客户端组件

1. **RankedUI** (`utils/rankedUI.js`)
   - 排位赛界面管理
   - 匹配状态显示
   - 结果展示

2. **MultiplayerManager** (扩展)
   - 排位赛消息处理
   - 服务器通信
   - 状态同步

## 🚀 快速开始

### 1. 启动服务器

```bash
cd server
npm install
npm start
```

服务器将在 `http://localhost:3000` 启动。

### 2. 测试系统

运行系统测试：
```bash
cd server
node test-ranked-system.js
```

### 3. 客户端测试

打开 `test-ranked-client.html` 在浏览器中测试排位赛功能。

## 📊 段位系统

### 段位等级

| 段位 | 积分范围 | 分段 | 颜色 | 图标 |
|------|----------|------|------|------|
| 青铜 | 0-1099 | 5段 | #CD7F32 | 🥉 |
| 白银 | 1100-1299 | 5段 | #C0C0C0 | 🥈 |
| 黄金 | 1300-1499 | 5段 | #FFD700 | 🥇 |
| 铂金 | 1500-1699 | 4段 | #E5E4E2 | 💎 |
| 钻石 | 1700-1999 | 4段 | #B9F2FF | 💠 |
| 大师 | 2000+ | 1段 | #FF6B6B | 👑 |

### 积分计算规则

- **基础K因子**: 32
- **排名倍数**:
  - 第1名: +100%积分
  - 第2名: +30%积分
  - 第3名: -30%积分
  - 第4名: -100%积分
- **积分变化范围**: -50 到 +50分

## 🎮 匹配系统

### 匹配算法

1. **积分匹配**: 优先匹配相近积分的玩家
2. **等待时间扩展**: 等待时间越长，匹配范围越大
3. **匹配质量评估**: 计算匹配质量分数

### 匹配范围

- **青铜-白银**: ±80分基础范围
- **黄金**: ±100分基础范围
- **铂金**: ±120分基础范围
- **钻石-大师**: ±150分基础范围

每30秒等待时间增加50分匹配范围，最大400分。

## 🏆 排行榜系统

### 排名计算

- 按积分降序排列
- 相同积分按总场次排序
- 只显示至少完成1场比赛的玩家

### API接口

```javascript
// 获取排行榜
GET /api/ranked/leaderboard?limit=50&offset=0

// 获取队列状态
GET /api/ranked/stats
```

## 📱 客户端集成

### 1. 初始化排位赛UI

```javascript
const rankedUI = new RankedUI();
rankedUI.init(multiplayerManager);

// 监听游戏开始事件
rankedUI.on('gameStarting', (data) => {
    // 启动排位赛游戏
    startRankedGame(data);
});

// 监听返回事件
rankedUI.on('back', () => {
    // 返回主菜单
    showMainMenu();
});
```

### 2. 显示排位赛界面

```javascript
// 显示排位赛主界面
rankedUI.showMainScreen();

// 加载用户排位信息
await rankedUI.loadUserRanking();
```

### 3. 处理排位赛消息

```javascript
// 扩展MultiplayerManager消息处理
multiplayerManager.on('queueJoined', (data) => {
    console.log('加入队列成功', data);
});

multiplayerManager.on('matchFound', (data) => {
    console.log('找到匹配', data);
});

multiplayerManager.on('rankedGameCompleted', (data) => {
    console.log('排位赛结束', data);
});
```

## 🔧 配置选项

### 服务器配置

```javascript
// 修改 server/game-server.js
const PORT = process.env.PORT || 3000;

// 修改匹配参数
const MATCH_SIZE = 4; // 4人对战
const MAX_WAIT_TIME = 300; // 最大等待5分钟
const MATCH_CHECK_INTERVAL = 5000; // 每5秒检查匹配
```

### 积分系统配置

```javascript
// 修改 server/rankingSystem.js
this.K_FACTOR = 32; // K因子
this.PLACEMENT_MULTIPLIERS = {
    1: 1.0,    // 第1名倍数
    2: 0.3,    // 第2名倍数
    3: -0.3,   // 第3名倍数
    4: -1.0    // 第4名倍数
};
```

## 📁 数据存储

### 数据文件结构

```
server/data/
├── users.json          # 用户数据
├── matches.json        # 比赛记录
├── rating_history.json # 积分历史
└── daily_stats.json    # 每日统计
```

### 数据备份

系统每5分钟自动保存数据，进程退出时也会保存数据。

手动保存：
```javascript
await databaseManager.saveData();
```

## 🐛 故障排除

### 常见问题

1. **端口占用**
   - 修改 `game-server.js` 中的端口号
   - 或设置环境变量 `PORT=3000`

2. **数据丢失**
   - 检查 `server/data/` 目录权限
   - 确保进程正常退出以保存数据

3. **匹配超时**
   - 检查服务器日志
   - 调整匹配参数

### 调试模式

启用详细日志：
```javascript
console.log('调试信息:', data);
```

## 🔄 更新和维护

### 版本更新

1. 备份数据文件
2. 更新代码
3. 重启服务器
4. 验证功能正常

### 性能监控

- 监控内存使用
- 检查匹配队列长度
- 观察数据库文件大小

## 📞 技术支持

如有问题，请检查：
1. 服务器日志输出
2. 客户端控制台错误
3. 网络连接状态
4. 数据文件完整性

---

🎉 **恭喜！** 花砖物语排位赛系统已成功部署并可以使用。享受竞技游戏的乐趣吧！
