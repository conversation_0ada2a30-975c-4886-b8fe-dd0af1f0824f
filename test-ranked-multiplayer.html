<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>花砖物语排位赛测试工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
        }
        .player-panel {
            display: inline-block;
            width: 280px;
            margin: 10px;
            padding: 15px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            vertical-align: top;
        }
        .player-header {
            font-weight: bold;
            margin-bottom: 10px;
            padding: 8px;
            background: #f8f9fa;
            border-radius: 5px;
            text-align: center;
        }
        .status {
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            margin: 5px 0;
            text-align: center;
        }
        .status.disconnected { background: #ffebee; color: #c62828; }
        .status.connected { background: #e8f5e8; color: #2e7d32; }
        .status.in-queue { background: #fff3e0; color: #ef6c00; }
        .status.matched { background: #e3f2fd; color: #1565c0; }
        
        button {
            width: 100%;
            padding: 8px;
            margin: 3px 0;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn-connect { background: #4CAF50; color: white; }
        .btn-disconnect { background: #f44336; color: white; }
        .btn-join-queue { background: #ff9800; color: white; }
        .btn-leave-queue { background: #9e9e9e; color: white; }
        
        .info {
            font-size: 12px;
            margin: 5px 0;
            padding: 5px;
            background: #f8f9fa;
            border-radius: 3px;
        }
        .log {
            height: 200px;
            overflow-y: auto;
            background: #263238;
            color: #4fc3f7;
            padding: 10px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            margin-top: 20px;
        }
        .controls {
            text-align: center;
            margin: 20px 0;
            padding: 15px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .controls button {
            width: auto;
            margin: 0 10px;
            padding: 10px 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏆 花砖物语排位赛测试工具</h1>
            <p>模拟多个玩家进行排位匹配测试</p>
            <p><strong>排位赛需要4个玩家才能开始匹配</strong></p>
        </div>

        <div class="controls">
            <button onclick="quickTest()" style="background: #4CAF50; color: white; font-weight: bold;">🚀 快速测试匹配</button>
            <br><br>
            <button onclick="connectAll()" class="btn-connect">全部连接</button>
            <button onclick="joinAllQueue()" class="btn-join-queue">全部加入队列</button>
            <button onclick="leaveAllQueue()" class="btn-leave-queue">全部离开队列</button>
            <button onclick="disconnectAll()" class="btn-disconnect">全部断开</button>
            <button onclick="clearLog()" style="background: #607d8b; color: white;">清空日志</button>
        </div>

        <div id="players"></div>
        
        <div class="log" id="log"></div>
    </div>

    <script>
        const SERVER_URL = 'ws://localhost:3000';
        const PLAYER_COUNT = 4; // 排位赛需要4个玩家
        
        let players = [];
        
        // 初始化玩家
        function initPlayers() {
            const playersContainer = document.getElementById('players');
            
            for (let i = 1; i <= PLAYER_COUNT; i++) {
                const player = {
                    id: i,
                    name: `测试玩家${i}`,
                    socket: null,
                    status: 'disconnected',
                    playerId: null,
                    queueInfo: null
                };
                
                players.push(player);
                
                // 创建玩家面板
                const panel = document.createElement('div');
                panel.className = 'player-panel';
                panel.id = `player-${i}`;
                panel.innerHTML = `
                    <div class="player-header">${player.name}</div>
                    <div class="status disconnected" id="status-${i}">未连接</div>
                    <div class="info" id="info-${i}">等待连接...</div>
                    <button onclick="connectPlayer(${i})" class="btn-connect">连接</button>
                    <button onclick="disconnectPlayer(${i})" class="btn-disconnect">断开</button>
                    <button onclick="joinQueue(${i})" class="btn-join-queue">加入队列</button>
                    <button onclick="leaveQueue(${i})" class="btn-leave-queue">离开队列</button>
                `;
                
                playersContainer.appendChild(panel);
            }
        }
        
        // 连接玩家
        function connectPlayer(playerId) {
            const player = players[playerId - 1];
            if (player.socket) {
                log(`玩家${playerId} 已经连接`);
                return;
            }
            
            try {
                player.socket = new WebSocket(SERVER_URL, ['game-protocol']);
                
                player.socket.onopen = () => {
                    player.status = 'connected';
                    updatePlayerStatus(playerId);
                    log(`玩家${playerId} 连接成功`);
                    
                    // 生成唯一的玩家ID
                    const generatedPlayerId = `test_player_${playerId}_${Date.now()}`;

                    // 发送认证消息
                    const authMessage = {
                        type: 'auth',
                        data: {
                            playerId: generatedPlayerId,
                            playerInfo: {
                                nickName: player.name,
                                avatarUrl: `https://example.com/avatar${playerId}.jpg`
                            }
                        }
                    };

                    log(`玩家${playerId} 发送认证消息，ID: ${generatedPlayerId}`);
                    player.socket.send(JSON.stringify(authMessage));
                };
                
                player.socket.onmessage = (event) => {
                    const message = JSON.parse(event.data);
                    handleMessage(playerId, message);
                };
                
                player.socket.onclose = () => {
                    player.status = 'disconnected';
                    player.socket = null;
                    player.playerId = null;
                    updatePlayerStatus(playerId);
                    log(`玩家${playerId} 连接断开`);
                };
                
                player.socket.onerror = (error) => {
                    log(`玩家${playerId} 连接错误: ${error}`);
                };
                
            } catch (error) {
                log(`玩家${playerId} 连接失败: ${error.message}`);
            }
        }
        
        // 断开玩家
        function disconnectPlayer(playerId) {
            const player = players[playerId - 1];
            if (player.socket) {
                player.socket.close();
            }
        }
        
        // 加入队列
        function joinQueue(playerId) {
            const player = players[playerId - 1];
            if (!player.socket || !player.playerId) {
                log(`玩家${playerId} 未连接或未认证`);
                return;
            }
            
            const message = {
                type: 'joinRankedQueue',
                data: {
                    playerId: player.playerId,
                    playerInfo: {
                        nickName: player.name,
                        avatarUrl: `https://example.com/avatar${playerId}.jpg`
                    }
                }
            };
            
            player.socket.send(JSON.stringify(message));
            log(`玩家${playerId} 尝试加入排位队列`);
        }
        
        // 离开队列
        function leaveQueue(playerId) {
            const player = players[playerId - 1];
            if (!player.socket || !player.playerId) {
                return;
            }
            
            const message = {
                type: 'leaveRankedQueue',
                data: {
                    playerId: player.playerId
                }
            };
            
            player.socket.send(JSON.stringify(message));
            log(`玩家${playerId} 离开排位队列`);
        }
        
        // 处理服务器消息
        function handleMessage(playerId, message) {
            const player = players[playerId - 1];

            // 调试：显示收到的消息
            if (message.type !== 'queueUpdate') { // 避免队列更新消息刷屏
                log(`玩家${playerId} 收到消息: ${message.type}`, JSON.stringify(message.data));
            }

            switch (message.type) {
                case 'authSuccess':
                    player.playerId = message.data.playerId;
                    if (!player.playerId) {
                        log(`⚠️ 警告: 玩家${playerId} 收到空的playerId!`);
                        // 使用我们自己生成的ID作为备用
                        player.playerId = `test_player_${playerId}_${Date.now()}`;
                    }
                    updatePlayerInfo(playerId, `认证成功: ${player.playerId}`);
                    log(`玩家${playerId} 认证成功: ${player.playerId}`);
                    break;
                    
                case 'queueJoined':
                    player.status = 'in-queue';
                    player.queueInfo = message.data;
                    updatePlayerStatus(playerId);
                    updatePlayerInfo(playerId, `队列位置: ${message.data.position || '未知'}`);
                    log(`玩家${playerId} 加入队列成功`);
                    break;
                    
                case 'queueUpdate':
                    player.queueInfo = message.data;
                    updatePlayerInfo(playerId, `队列位置: ${message.data.position}, 等待: ${message.data.waitTime}s`);
                    break;
                    
                case 'matchFound':
                    player.status = 'matched';
                    updatePlayerStatus(playerId);
                    updatePlayerInfo(playerId, `匹配成功! 匹配ID: ${message.data.matchId}`);
                    log(`玩家${playerId} 匹配成功: ${message.data.matchId}`);
                    break;
                    
                case 'queueLeft':
                    player.status = 'connected';
                    player.queueInfo = null;
                    updatePlayerStatus(playerId);
                    updatePlayerInfo(playerId, '已离开队列');
                    log(`玩家${playerId} 离开队列`);
                    break;
                    
                case 'error':
                    log(`玩家${playerId} 错误: ${message.data.message || message.data}`);
                    break;
                    
                default:
                    log(`玩家${playerId} 收到消息: ${message.type}`);
            }
        }
        
        // 更新玩家状态
        function updatePlayerStatus(playerId) {
            const player = players[playerId - 1];
            const statusElement = document.getElementById(`status-${playerId}`);
            
            statusElement.className = `status ${player.status}`;
            
            switch (player.status) {
                case 'disconnected':
                    statusElement.textContent = '未连接';
                    break;
                case 'connected':
                    statusElement.textContent = '已连接';
                    break;
                case 'in-queue':
                    statusElement.textContent = '队列中';
                    break;
                case 'matched':
                    statusElement.textContent = '已匹配';
                    break;
            }
        }
        
        // 更新玩家信息
        function updatePlayerInfo(playerId, info) {
            const infoElement = document.getElementById(`info-${playerId}`);
            infoElement.textContent = info;
        }
        
        // 批量操作
        function connectAll() {
            for (let i = 1; i <= PLAYER_COUNT; i++) {
                setTimeout(() => connectPlayer(i), i * 200);
            }
        }
        
        function disconnectAll() {
            for (let i = 1; i <= PLAYER_COUNT; i++) {
                disconnectPlayer(i);
            }
        }
        
        function joinAllQueue() {
            for (let i = 1; i <= PLAYER_COUNT; i++) {
                setTimeout(() => joinQueue(i), i * 100);
            }
        }
        
        function leaveAllQueue() {
            for (let i = 1; i <= PLAYER_COUNT; i++) {
                leaveQueue(i);
            }
        }
        
        // 日志功能
        function log(message, details) {
            const logElement = document.getElementById('log');
            const time = new Date().toLocaleTimeString();

            let logHtml = `<div>[${time}] ${message}`;

            // 如果有详细信息，添加到日志中
            if (details) {
                logHtml += `<span style="color: #888; font-size: 0.9em;"> - ${details}</span>`;
            }

            logHtml += `</div>`;

            logElement.innerHTML += logHtml;
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        // 快速测试匹配
        function quickTest() {
            log('🚀 开始快速测试匹配...');
            log('1. 连接所有玩家...');
            connectAll();

            setTimeout(() => {
                log('2. 等待连接完成，然后加入队列...');
                joinAllQueue();
            }, 2000);

            setTimeout(() => {
                log('3. 匹配应该在几秒内完成！');
            }, 3000);
        }

        // 初始化
        window.onload = function() {
            initPlayers();
            log('排位赛测试工具已启动');
            log('请确保游戏服务器在 localhost:3000 运行');
            log('');
            log('💡 使用说明：');
            log('1. 点击"🚀 快速测试匹配"按钮一键测试');
            log('2. 或者手动操作：全部连接 → 全部加入队列');
            log('3. 排位赛需要4个玩家才能开始匹配');
        };
    </script>
</body>
</html>
